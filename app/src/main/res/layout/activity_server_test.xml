<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="服务器连接测试 (端口8083)"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="测试按钮"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_test_all"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🔄 完整服务器测试"
                    android:layout_marginBottom="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_test_connection"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="🔗 连接测试"
                        android:layout_marginEnd="4dp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_test_categories"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="📂 分类API"
                        android:layout_marginStart="4dp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="8dp">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_test_projects"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="📋 项目API"
                        android:layout_marginEnd="4dp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_test_download"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="⬇️ 下载测试"
                        android:layout_marginStart="4dp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="测试结果"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_results"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="点击测试按钮开始...\n\n服务器地址: http://192.168.110.80:8083\nAPI端点: /api/client/"
                    android:textSize="14sp"
                    android:fontFamily="monospace"
                    android:minHeight="400dp"
                    android:gravity="top" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
