package com.example.coloringproject.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.example.coloringproject.databinding.FragmentMyGalleryBinding
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import android.util.Log
import androidx.appcompat.app.AlertDialog
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.example.coloringproject.R
import com.example.coloringproject.adapter.MyGalleryAdapter
import com.example.coloringproject.utils.ProjectSaveManager
import com.example.coloringproject.utils.ProjectProgress
import com.example.coloringproject.utils.HybridResourceManager
import com.example.coloringproject.utils.LibraryEventManager
import com.example.coloringproject.interfaces.LibraryRefreshListener
import com.example.coloringproject.viewmodel.MyGalleryViewModel
import com.example.coloringproject.viewmodel.GalleryFilter
import com.google.android.material.chip.Chip
import com.google.android.material.chip.ChipGroup
import com.google.android.material.floatingactionbutton.FloatingActionButton

/**
 * My Gallery Fragment - 显示进行中和已完成的作品
 * 用户的个人作品集
 */
class MyGalleryFragment : Fragment(), LibraryRefreshListener {
    
    private val viewModel: MyGalleryViewModel by viewModels()
    private var _binding: FragmentMyGalleryBinding? = null
    private val binding get() = _binding!!

    private lateinit var projectSaveManager: ProjectSaveManager
    private lateinit var adapter: MyGalleryAdapter
    
    // 项目选择回调 - 🚀 Gallery修复：改为HybridProject类型，与Library保持一致
    var onProjectSelected: ((HybridResourceManager.HybridProject, android.widget.ImageView) -> Unit)? = null
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMyGalleryBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        Log.d("MyGalleryFragment", "🚀 onViewCreated 开始初始化")

        projectSaveManager = ProjectSaveManager(requireContext())
        Log.d("MyGalleryFragment", "✅ ProjectSaveManager 创建完成")

        // 设置ViewModel的SaveManager
        viewModel.setSaveManager(projectSaveManager)
        Log.d("MyGalleryFragment", "✅ ViewModel SaveManager 设置完成")

        setupRecyclerView()
        setupSwipeRefresh()
        setupFilterChips()
        setupObservers()
        setupFab()

        // 注册Library刷新监听器
        LibraryEventManager.registerListener(this)
        Log.d("MyGalleryFragment", "✅ Library刷新监听器注册完成")

        Log.d("MyGalleryFragment", "🚀 准备调用 loadSavedProjects")
        loadSavedProjects()
    }


    private fun setupRecyclerView() {
        // 使用网格布局，每行2个项目
        val spanCount = if (resources.configuration.screenWidthDp >= 600) 3 else 2
        val layoutManager = GridLayoutManager(requireContext(), spanCount)
        binding.recyclerViewMyGallery.layoutManager = layoutManager
        
        adapter = MyGalleryAdapter(
            onProjectClick = { project, imageView ->
                // 模块2：智能确认对话框策略
                handleProjectClick(project, imageView)
            },
            onProjectLongClick = { project ->
                showProjectOptions(project)
            },
            onDeleteClick = { project ->
                deleteProject(project)
            },
            onShareClick = { project ->
                shareProject(project)
            }
        )
        binding.recyclerViewMyGallery.adapter = adapter
    }
    
    private fun setupSwipeRefresh() {
        binding.swipeRefreshMyGallery.setOnRefreshListener {
            loadSavedProjects()
        }
        binding.swipeRefreshMyGallery.setColorSchemeResources(
            R.color.progress_color,
            R.color.toolbar_background,
            R.color.completed_color
        )
    }
    
    private fun setupFilterChips() {
        val filters = listOf("全部", "进行中", "已完成", "最近修改")

        filters.forEach { filter ->
            val chip = Chip(requireContext())
            chip.text = filter
            chip.isCheckable = true
            chip.setOnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    // 取消其他chip的选中状态
                    uncheckOtherChips(chip)
                    filterProjects(filter)
                }
            }
            binding.chipGroupFilter.addView(chip)
        }

        // 默认选中"全部"
        (binding.chipGroupFilter.getChildAt(0) as Chip).isChecked = true
    }

    /**
     * 取消其他chip的选中状态，确保单选行为
     */
    private fun uncheckOtherChips(selectedChip: Chip) {
        for (i in 0 until binding.chipGroupFilter.childCount) {
            val chip = binding.chipGroupFilter.getChildAt(i) as Chip
            if (chip != selectedChip) {
                chip.isChecked = false
            }
        }
    }
    
    private fun setupObservers() {
        viewModel.projects.observe(viewLifecycleOwner) { projects ->
            adapter.updateProjects(projects)
            updateUI(projects)
        }
        
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.swipeRefreshMyGallery.isRefreshing = isLoading
        }
        
        viewModel.error.observe(viewLifecycleOwner) { error ->
            error?.let {
            }
        }
    }
    
    private fun setupFab() {
        binding.fabExportAll.setOnClickListener {
            exportAllCompletedProjects()
        }
    }
    
    private fun loadSavedProjects() {
        Log.d("MyGalleryFragment", "🚀 开始加载保存的项目")
        // 🚀 Gallery优化：使用轻量级加载，跳过JSON解析
        lifecycleScope.launch {
            try {
                Log.d("MyGalleryFragment", "设置加载状态为true")
                viewModel.setLoading(true)
                val loadStart = System.currentTimeMillis()

                // 在IO线程中执行轻量级文件操作
                val galleryProjects = withContext(Dispatchers.IO) {
                    val projects = projectSaveManager.getProjectsForGallery()
                    Log.d("MyGalleryFragment", "🚀 [Gallery优化] 轻量级加载了 ${projects.size} 个项目")
                    projects
                }

                // 转换为ProjectProgress格式（保持兼容性）
                val savedProjects = galleryProjects.map { galleryProject ->
                    ProjectProgress(
                        projectName = galleryProject.projectName,
                        filledRegions = emptySet(), // Gallery不需要具体进度
                        totalRegions = 0, // Gallery不需要总区域数
                        lastModified = galleryProject.lastModified,
                        progressPercentage = galleryProject.progressPercentage, // 使用真实的进度数据
                        isCompleted = galleryProject.progressPercentage >= 100, // 根据进度判断是否完成
                        previewImagePath = galleryProject.previewImagePath // 使用实际的预览图片路径
                    )
                }

                val loadTime = System.currentTimeMillis() - loadStart
                Log.d("MyGalleryFragment", "🚀 [Gallery优化] 总加载时间: ${loadTime}ms，显示 ${savedProjects.size} 个项目")

                // 在主线程中更新UI
                Log.d("MyGalleryFragment", "设置项目到ViewModel，项目数量: ${savedProjects.size}")
                savedProjects.forEachIndexed { index, project ->
                    Log.d("MyGalleryFragment", "项目 $index: ${project.projectName}, 进度: ${project.progressPercentage}%, 完成: ${project.isCompleted}")
                }
                viewModel.setProjects(savedProjects)

                // 验证ViewModel中的数据
                val allProjectsInViewModel = viewModel.getAllProjects()
                Log.d("MyGalleryFragment", "ViewModel中的项目数量: ${allProjectsInViewModel.size}")
            } catch (e: Exception) {
                Log.e("MyGalleryFragment", "❌ Gallery轻量级加载失败", e)
                viewModel.setError(e.message ?: "加载失败")
            } finally {
                Log.d("MyGalleryFragment", "设置加载状态为false")
                viewModel.setLoading(false)
            }
        }
    }
    
    private fun filterProjects(filter: String) {
        Log.d("MyGalleryFragment", "应用筛选器: $filter")

        val allProjects = viewModel.getAllProjects()
        Log.d("MyGalleryFragment", "所有项目数量: ${allProjects.size}")

        // 🔧 修复：使用ViewModel的applyFilter方法，而不是setProjects
        val galleryFilter = when (filter) {
            "进行中" -> {
                Log.d("MyGalleryFragment", "筛选进行中项目")
                GalleryFilter.IN_PROGRESS
            }
            "已完成" -> {
                Log.d("MyGalleryFragment", "筛选已完成项目")
                GalleryFilter.COMPLETED
            }
            "最近修改" -> {
                Log.d("MyGalleryFragment", "筛选最近修改项目")
                GalleryFilter.RECENT
            }
            else -> {
                Log.d("MyGalleryFragment", "显示全部项目")
                GalleryFilter.ALL
            }
        }

        // 使用ViewModel的applyFilter方法，保持allProjects不变
        viewModel.applyFilter(galleryFilter)

        // 验证筛选后的结果
        val filteredProjects = viewModel.projects.value ?: emptyList()
        Log.d("MyGalleryFragment", "筛选后项目数量: ${filteredProjects.size}")
    }
    
    private fun updateUI(projects: List<ProjectProgress>) {
        Log.d("MyGalleryFragment", "更新UI，项目数量: ${projects.size}")

        if (projects.isEmpty()) {
            binding.tvEmptyState.visibility = View.VISIBLE
            binding.recyclerViewMyGallery.visibility = View.GONE

            // 根据当前筛选器显示不同的空状态消息
            val currentFilter = getCurrentSelectedFilter()
            binding.tvEmptyState.text = when (currentFilter) {
                "进行中" -> "没有进行中的项目\n所有项目都已完成！"
                "已完成" -> "还没有完成的项目\n继续努力完成你的作品吧！"
                "最近修改" -> "还没有保存的作品\n开始你的第一个填色项目吧！"
                else -> "还没有保存的作品\n开始你的第一个填色项目吧！"
            }
        } else {
            binding.tvEmptyState.visibility = View.GONE
            binding.recyclerViewMyGallery.visibility = View.VISIBLE

            // 更新统计信息 - 基于所有项目，不是筛选后的项目
            val allProjects = viewModel.getAllProjects()
            val completedCount = allProjects.count { it.isCompleted }
            val inProgressCount = allProjects.size - completedCount
            binding.tvGalleryStats.text = "总计 ${allProjects.size} 个作品 | 已完成 $completedCount | 进行中 $inProgressCount"
        }
    }

    /**
     * 获取当前选中的筛选器
     */
    private fun getCurrentSelectedFilter(): String {
        for (i in 0 until binding.chipGroupFilter.childCount) {
            val chip = binding.chipGroupFilter.getChildAt(i) as Chip
            if (chip.isChecked) {
                return chip.text.toString()
            }
        }
        return "全部"
    }
    
    private fun showProjectOptions(project: ProjectProgress) {
        // TODO: 显示项目选项对话框（分享、删除、导出等）
    }

    private fun deleteProject(project: ProjectProgress) {
        // TODO: 实现删除项目功能
    }

    private fun shareProject(project: ProjectProgress) {
        // TODO: 实现分享项目功能
    }
    
    private fun exportAllCompletedProjects() {
        val completedProjects = viewModel.projects.value?.filter { it.isCompleted } ?: emptyList()
        
        if (completedProjects.isEmpty()) {
            return
        }
        
        // TODO: 实现批量导出功能
    }
    
    companion object {
        fun newInstance(): MyGalleryFragment {
            return MyGalleryFragment()
        }
    }

    /**
     * 模块2：处理项目点击 - 智能确认对话框策略
     * 基于竞品设计：用确认对话框掩盖数据加载时间
     */
    private fun handleProjectClick(project: ProjectProgress, imageView: android.widget.ImageView) {
        Log.d("MyGalleryFragment", "用户点击项目: ${project.projectName}")

        // 检查项目数据是否已经缓存
        if (isProjectDataCached(project.projectName)) {
            Log.d("MyGalleryFragment", "项目数据已缓存，直接进入: ${project.projectName}")
            // 已缓存，直接进入涂色页面 - 🚀 Gallery修复：转换为HybridProject
            val hybridProject = convertToHybridProject(project)
            onProjectSelected?.invoke(hybridProject, imageView)
        } else {
            Log.d("MyGalleryFragment", "项目数据未缓存，显示确认对话框: ${project.projectName}")
            // 未缓存，显示智能确认对话框
            showSmartConfirmDialog(project, imageView)
        }
    }

    /**
     * 检查项目数据是否已缓存
     * 模块4：实现真正的缓存检查
     */
    private fun isProjectDataCached(projectId: String): Boolean {
        val smartPreloadManager = com.example.coloringproject.utils.SmartPreloadManager.getInstance(requireContext())
        val isPreloaded = smartPreloadManager.isProjectPreloaded(projectId)

        Log.d("MyGalleryFragment", "检查项目 $projectId 预加载状态: $isPreloaded")
        return isPreloaded
    }

    /**
     * 智能确认对话框 - 核心创新功能 + Gallery优化修复
     * 确保从Gallery进入时加载完整的项目数据
     */
    private fun showSmartConfirmDialog(project: ProjectProgress, imageView: android.widget.ImageView) {
        // 🚀 Gallery优化修复：确保加载完整项目数据
        var detailedProject: ProjectProgress? = null
        var isLoading = true

        val dialog = MaterialAlertDialogBuilder(requireContext())
            .setTitle("继续涂色")
            .setMessage("要继续 ${project.projectName} 的涂色吗？\n正在加载进度信息...")
//            .setPositiveButton("继续涂色") { _, _ ->
//                // 用户确认时，确保使用完整的项目数据
//                lifecycleScope.launch {
//                    try {
//                        val finalProject = if (detailedProject != null && !isLoading) {
//                            detailedProject!!
//                        } else {
//                            // 如果还在加载或加载失败，立即加载单个项目数据
//                            withContext(Dispatchers.IO) {
//                                projectSaveManager.getSingleProjectProgress(project.projectName)
//                                    ?: project // 如果找不到，使用原始项目（可能会失败，但至少尝试）
//                            }
//                        }
//
//                        Log.d("MyGalleryFragment", "🚀 [Gallery修复] 使用完整项目数据: ${finalProject.projectName}")
//                        Log.d("MyGalleryFragment", "项目详情: 填色区域=${finalProject.filledRegions.size}, 总区域=${finalProject.totalRegions}, 进度=${finalProject.progressPercentage}%")
//
//                        // 🚀 Gallery修复：转换为HybridProject格式，使用和Library相同的启动方法
//                        val hybridProject = convertToHybridProject(finalProject)
//                        onProjectSelected?.invoke(hybridProject, imageView)
//                    } catch (e: Exception) {
//                        Log.e("MyGalleryFragment", "加载完整项目数据失败: ${project.projectName}", e)
//                        // 即使失败也尝试使用原始项目 - 🚀 Gallery修复：转换为HybridProject
//                        val fallbackHybridProject = convertToHybridProject(project)
//                        onProjectSelected?.invoke(fallbackHybridProject, imageView)
//                    }
//                }
//            }
            .setNegativeButton("取消") { dialog, _ ->
                Log.d("MyGalleryFragment", "用户取消涂色: ${project.projectName}")
                dialog.dismiss()
            }
            .setNeutralButton("快速进入") { _, _ ->
                // 快速进入，跳过详细数据加载
                Log.d("MyGalleryFragment", "🚀 [快速进入] 用户选择快速进入: ${project.projectName}")
                val hybridProject = convertToHybridProject(project)
                onProjectSelected?.invoke(hybridProject, imageView)
            }
            .setCancelable(true)
            .create()

        // 显示对话框
        dialog.show()

        // 🚀 Gallery优化：使用单项目加载，大幅提升性能
        lifecycleScope.launch {
            try {
                val loadStart = System.currentTimeMillis()
                detailedProject = withContext(Dispatchers.IO) {
                    // 只加载当前项目，而不是所有项目
                    projectSaveManager.getSingleProjectProgress(project.projectName)
                }
                isLoading = false

                val loadTime = System.currentTimeMillis() - loadStart
                Log.d("MyGalleryFragment", "🚀 [性能优化] 单项目加载耗时: ${loadTime}ms")

                // 更新对话框消息显示加载完成
                if (dialog.isShowing && detailedProject != null) {
                    withContext(Dispatchers.Main) {
                        dialog.setMessage("要继续 ${project.projectName} 的涂色吗？\n当前进度：${detailedProject!!.progressPercentage}%")
                    }
                }

                Log.d("MyGalleryFragment", "🚀 [Gallery优化] 详细信息预加载完成: ${project.projectName}")
            } catch (e: Exception) {
                isLoading = false
                Log.e("MyGalleryFragment", "预加载详细信息失败: ${project.projectName}", e)
            }
        }
    }

    /**
     * 后台预加载项目数据
     * 这是智能对话框的核心：用户思考时间 = 数据加载时间
     */
    private fun preloadProjectDataInBackground(project: ProjectProgress, dialog: AlertDialog) {
        lifecycleScope.launch {
            try {
                val startTime = System.currentTimeMillis()
                Log.d("MyGalleryFragment", "开始后台加载项目数据: ${project.projectName}")

                // 模拟/实际的项目数据加载
                withContext(Dispatchers.IO) {
                    // 模拟数据加载时间（实际项目中这里会是真正的数据加载）
                    delay(500) // 500ms模拟加载时间

                    // TODO: 后续模块中这里会预加载：
                    // 1. JSON数据
                    // 2. Outline图片
                    // 3. Region图片
                    Log.d("MyGalleryFragment", "项目数据加载完成: ${project.projectName}")
                }

                val loadTime = System.currentTimeMillis() - startTime
                Log.d("MyGalleryFragment", "项目数据加载耗时: ${loadTime}ms")

                // 如果加载太快，稍微延迟一下，避免对话框闪烁
                if (loadTime < 300) {
                    delay(300 - loadTime)
                }

                // 数据加载完成，更新对话框按钮状态（可选的视觉反馈）
                if (dialog.isShowing) {
                    dialog.getButton(AlertDialog.BUTTON_POSITIVE)?.apply {
                        text = "开始涂色 ✓"
                        // 可以添加一个微妙的视觉提示，表示数据已准备好
                    }
                }

            } catch (e: Exception) {
                Log.e("MyGalleryFragment", "预加载项目数据失败: ${project.projectName}", e)

                // 加载失败时的处理
                if (dialog.isShowing) {
                    dialog.getButton(AlertDialog.BUTTON_POSITIVE)?.apply {
                        text = "重试"
                        isEnabled = true
                    }
                }
            }
        }
    }

    /**
     * 🚀 Gallery修复：将ProjectProgress转换为HybridProject
     * 确保使用和Library相同的启动方法
     */
    private fun convertToHybridProject(projectProgress: ProjectProgress): HybridResourceManager.HybridProject {
        // 使用统一的项目ID
        val unifiedId = com.example.coloringproject.utils.ProjectNameUtils.getUnifiedProjectId(projectProgress)
        return HybridResourceManager.HybridProject(
            id = unifiedId,
            name = unifiedId,
            displayName = unifiedId, // 使用统一的ID保持一致性
            description = "已保存的填色项目",
            category = "saved",
            difficulty = "medium",
            totalRegions = projectProgress.totalRegions,
            totalColors = 0, // Gallery不需要具体颜色数
            estimatedTime = 30, // 默认30分钟
            thumbnailUrl = null,
            previewUrl = null,
            resourceType = HybridResourceManager.Companion.ResourceType.DOWNLOADED,
            resourceSource = HybridResourceManager.Companion.ResourceSource.BUILT_IN, // 修复：Gallery项目实际来自assets
            version = "1.0",
            fileSize = 0L, // Gallery不需要文件大小
            isDownloaded = true,
            isBuiltIn = false,
            downloadProgress = 100f,
            tags = listOf("saved"),
            releaseDate = null,
            popularity = 0,
            rating = 0f,
            hasPreloadedData = projectProgress.filledRegions.isNotEmpty() // 有填色进度就认为有预加载数据
        )
    }

    // ========== LibraryRefreshListener 接口实现 ==========

    override fun onProjectProgressUpdated(projectId: String, hasProgress: Boolean, progressPercentage: Int) {
        Log.d("MyGalleryFragment", "🔄 收到项目进度更新通知: $projectId, 进度: ${progressPercentage}%")

        if (isAdded && view != null) {
            lifecycleScope.launch {
                try {
                    // 刷新ViewModel中的项目进度
                    viewModel.updateProjectProgress(projectId, progressPercentage, progressPercentage >= 100)

                    // 刷新适配器中的特定项目
                    refreshProjectInAdapter(projectId)

                    Log.d("MyGalleryFragment", "✅ 项目进度更新完成: $projectId")
                } catch (e: Exception) {
                    Log.e("MyGalleryFragment", "❌ 更新项目进度失败: $projectId", e)
                }
            }
        }
    }

    override fun onProjectPreviewUpdated(projectId: String, previewImagePath: String?) {
        Log.d("MyGalleryFragment", "🖼️ 收到项目预览图更新通知: $projectId, 路径: $previewImagePath")

        if (isAdded && view != null) {
            lifecycleScope.launch {
                try {
                    // 刷新适配器中的项目缩略图
                    refreshProjectThumbnail(projectId)

                    Log.d("MyGalleryFragment", "✅ 项目缩略图刷新完成: $projectId")
                } catch (e: Exception) {
                    Log.e("MyGalleryFragment", "❌ 更新项目缩略图失败: $projectId", e)
                }
            }
        }
    }

    override fun onProjectCompleted(projectId: String) {
        Log.d("MyGalleryFragment", "🎉 收到项目完成通知: $projectId")

        if (isAdded && view != null) {
            lifecycleScope.launch {
                try {
                    // 标记项目为已完成
                    viewModel.updateProjectProgress(projectId, 100, true)

                    // 刷新适配器显示
                    refreshProjectInAdapter(projectId)
                    refreshProjectThumbnail(projectId)

                    Log.d("MyGalleryFragment", "✅ 项目完成状态更新完成: $projectId")
                } catch (e: Exception) {
                    Log.e("MyGalleryFragment", "❌ 更新项目完成状态失败: $projectId", e)
                }
            }
        }
    }

    override fun refreshLibrary() {
        Log.d("MyGalleryFragment", "🔄 收到刷新Gallery的通知")

        if (isAdded && view != null) {
            loadSavedProjects()
        }
    }

    // ========== 适配器刷新辅助方法 ==========

    /**
     * 刷新适配器中的特定项目
     */
    private fun refreshProjectInAdapter(projectId: String) {
        if (::adapter.isInitialized) {
            adapter.refreshProject(projectId)
        }
    }

    /**
     * 刷新适配器中的项目缩略图
     */
    private fun refreshProjectThumbnail(projectId: String) {
        if (::adapter.isInitialized) {
            adapter.refreshProjectThumbnail(projectId)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()

        // 取消注册Library刷新监听器
        LibraryEventManager.unregisterListener(this)

        _binding = null
    }
}
