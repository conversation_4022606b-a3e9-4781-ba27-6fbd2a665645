package com.example.coloringproject.ui

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2
import com.example.coloringproject.adapter.CategoryPagerAdapter
import com.example.coloringproject.databinding.FragmentCategorizedProjectsBinding
import com.example.coloringproject.manager.ProjectDataManager
import com.example.coloringproject.manager.CategoryMappingManager
import com.example.coloringproject.utils.HybridResourceManager
import com.example.coloringproject.utils.SmartPreloadManager
import com.google.android.material.tabs.TabLayoutMediator
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay

/**
 * 分类项目主Fragment
 * 使用TabLayout + ViewPager2 + Fragment架构
 */
class CategorizedProjectsFragment : Fragment() {
    
    companion object {
        private const val TAG = "CategorizedProjectsFragment"
        
        fun newInstance(): CategorizedProjectsFragment {
            return CategorizedProjectsFragment()
        }
    }
    
    private var _binding: FragmentCategorizedProjectsBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var projectDataManager: ProjectDataManager
    private lateinit var pagerAdapter: CategoryPagerAdapter
    private var tabLayoutMediator: TabLayoutMediator? = null

    // 模块3：智能预加载管理器
    private lateinit var smartPreloadManager: SmartPreloadManager

    // 项目选择回调
    var onProjectSelected: ((HybridResourceManager.HybridProject, android.widget.ImageView) -> Unit)? = null
    
    // 分类数据
    private var categories: List<String> = emptyList()
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentCategorizedProjectsBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        Log.d(TAG, "onViewCreated")
        
        // 初始化组件
        initializeComponents()
        
        // 设置UI
        setupUI()
        
        // 加载分类数据
        loadCategories()

        // 模块3：实施Library激进预加载策略
        implementLibraryAggressivePreloading()
    }
    
    private fun initializeComponents() {
        projectDataManager = ProjectDataManager.getInstance(requireContext())

        // 模块3：初始化智能预加载管理器
        smartPreloadManager = SmartPreloadManager.getInstance(requireContext())
    }
    
    private fun setupUI() {
        // 初始化ViewPager2适配器
        pagerAdapter = CategoryPagerAdapter(this) { project, imageView ->
            // 传递项目选择事件到父Activity
            onProjectSelected?.invoke(project, imageView)
        }
        
        binding.viewPager.adapter = pagerAdapter
        
        // 设置ViewPager2的预加载页面数
        binding.viewPager.offscreenPageLimit = 1
        
        // 设置页面切换监听
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                Log.d(TAG, "Page selected: $position")
            }
        })
    }
    
    private fun loadCategories() {
        lifecycleScope.launch {
            try {
                Log.d(TAG, "开始快速启动分类加载...")
                
                // 第一阶段：使用快速启动管理器立即显示分类
                val fastStartupManager = com.example.coloringproject.manager.FastStartupManager.getInstance(requireContext())
                val fastCategories = fastStartupManager.getFastStartupCategories()
                
                Log.d(TAG, "快速启动分类: ${fastCategories.joinToString(", ")}")
                categories = fastCategories
                setupTabsAndPages()
                
                Log.d(TAG, "✅ 分类已快速显示，开始后台优化...")
                
                // 第二阶段：后台优化分类列表
                lifecycleScope.launch {
                    try {
                        // 使用优先加载策略：本地分类优化，网络分类异步补充
                        val result = projectDataManager.getAvailableCategoriesWithPriority { networkCategories ->
                            // 网络分类加载完成的回调
                            lifecycleScope.launch {
                                try {
                                    if (!isAdded || _binding == null) {
                                        Log.w(TAG, "网络分类加载完成时Fragment已不活跃")
                                        return@launch
                                    }
                                    
                                    Log.d(TAG, "网络分类加载完成: ${networkCategories.joinToString(", ")}")
                                    
                                    if (networkCategories.isNotEmpty()) {
                                        // 将网络分类添加到现有分类列表
                                        val updatedCategories = categories + networkCategories
                                        categories = updatedCategories
                                        
                                        // 更新TabLayout和ViewPager
                                        updateTabsAndPages()
                                        
                                        Log.d(TAG, "网络分类已添加: ${networkCategories.size} 个新分类")
                                    }
                                } catch (e: Exception) {
                                    Log.e(TAG, "处理网络分类回调异常", e)
                                }
                            }
                        }
                        
                        result.fold(
                            onSuccess = { optimizedCategories ->
                                // 如果优化后的分类与快速启动分类不同，则更新
                                if (optimizedCategories != fastCategories) {
                                    lifecycleScope.launch {
                                        if (isAdded && _binding != null) {
                                            Log.d(TAG, "本地分类优化完成: ${optimizedCategories.joinToString(", ")}")
                                            categories = optimizedCategories
                                            updateTabsAndPages()
                                        }
                                    }
                                }
                            },
                            onFailure = { error ->
                                Log.e(TAG, "后台分类优化失败", error)
                                // 优化失败不影响已显示的快速启动分类
                            }
                        )
                    } catch (e: Exception) {
                        Log.e(TAG, "后台分类优化异常", e)
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "快速启动分类加载异常", e)
                // 如果快速启动失败，降级到默认分类
                setupDefaultCategories()
            }
        }
    }
    
    private fun setupTabsAndPages() {
        if (categories.isEmpty()) {
            Log.w(TAG, "分类列表为空，使用默认分类")
            setupDefaultCategories()
            return
        }
        
        // 更新适配器数据
        pagerAdapter.updateCategories(categories)
        
        // 设置TabLayout和ViewPager2的关联
        tabLayoutMediator?.detach()
        tabLayoutMediator = TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = formatCategoryDisplayName(categories[position])
        }
        tabLayoutMediator?.attach()
        
        Log.d(TAG, "TabLayout和ViewPager2设置完成，共${categories.size}个分类")
    }
    
    /**
     * 更新TabLayout和ViewPager（用于网络分类动态添加）
     */
    private fun updateTabsAndPages() {
        if (categories.isEmpty()) {
            Log.w(TAG, "分类列表为空，无法更新")
            return
        }
        
        // 更新适配器数据
        pagerAdapter.updateCategories(categories)
        
        // 重新设置TabLayout和ViewPager2的关联
        tabLayoutMediator?.detach()
        tabLayoutMediator = TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = formatCategoryDisplayName(categories[position])
        }
        tabLayoutMediator?.attach()
        
        Log.d(TAG, "TabLayout和ViewPager2更新完成，共${categories.size}个分类")
    }
    
    private fun setupDefaultCategories() {
        // 设置默认分类
        categories = listOf("animal", "building", "castle", "gardens", "treehouse")
        setupTabsAndPages()
    }
    
    private fun formatCategoryDisplayName(categoryName: String): String {
        return CategoryMappingManager.getCategoryDisplayName(categoryName)
    }
    
    /**
     * 刷新所有分类的项目数据
     */
    fun refreshAllCategories() {
        // 检查Fragment是否仍然活跃
        if (!isAdded || isDetached || _binding == null) {
            Log.w(TAG, "Fragment不活跃，跳过刷新")
            return
        }

        Log.d(TAG, "刷新所有分类的项目数据")

        // 使用协程延迟刷新，确保Fragment完全初始化
        lifecycleScope.launch {
            try {
                // 短暂延迟，确保ViewPager2和Fragment都已准备好
                kotlinx.coroutines.delay(100)

                // 再次检查Fragment状态
                if (!isAdded || _binding == null) {
                    Log.w(TAG, "延迟检查时Fragment已不活跃")
                    return@launch
                }

                // 刷新当前显示的所有CategoryProjectFragment
                for (i in 0 until categories.size) {
                    val fragment = pagerAdapter.getFragmentAt(i)
                    if (fragment != null && fragment.isAdded && !fragment.isDetached) {
                        fragment.refreshProjects()
                    } else {
                        Log.d(TAG, "跳过不活跃的Fragment: position $i")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "刷新分类时出错", e)
            }
        }
    }

    /**
     * 模块3：实施Library激进预加载策略
     * 基于竞品设计：Library页面激进预加载，实现"秒进"体验
     */
    private fun implementLibraryAggressivePreloading() {
        lifecycleScope.launch {
            try {
                // 短暂延迟，确保UI完全加载
                delay(200)

                Log.d(TAG, "开始Library激进预加载策略")

                // 获取当前分类
                val currentCategory = getCurrentCategory()
                Log.d(TAG, "当前分类: $currentCategory")

                // 获取当前分类的项目列表
                val currentCategoryProjects = getCurrentCategoryProjects(currentCategory)

                if (currentCategoryProjects.isNotEmpty()) {
                    Log.d(TAG, "开始动态预加载当前分类 $currentCategory")

                    // 使用动态内存管理，根据设备性能自动调整预加载数量
                    smartPreloadManager.preloadLibraryProjects(
                        category = currentCategory,
                        projectIds = currentCategoryProjects
                        // 不指定maxCount，使用动态配置
                    )
                }

                // 预加载其他分类的缩略图（低优先级）- 紧急修复：减少数量
                categories.filter { it != currentCategory }.take(2).forEach { category ->
                    launch {
                        val categoryProjects = getCategoryProjects(category)
                        if (categoryProjects.isNotEmpty()) {
                            Log.d(TAG, "预加载分类 $category 的缩略图（紧急修复：仅2个分类，每分类2个项目）")

                            // 紧急修复：仅预加载前2个项目的缩略图
                            smartPreloadManager.preloadGalleryProjects(
                                categoryProjects.take(2)
                            )
                        }
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "Library激进预加载失败", e)
            }
        }
    }

    /**
     * 获取当前分类
     */
    private fun getCurrentCategory(): String {
        val currentPosition = binding.viewPager.currentItem
        return if (currentPosition < categories.size) {
            categories[currentPosition]
        } else {
            categories.firstOrNull() ?: "animal"
        }
    }

    /**
     * 获取当前分类的项目列表
     */
    private suspend fun getCurrentCategoryProjects(category: String): List<String> {
        return getCategoryProjects(category)
    }

    /**
     * 获取指定分类的项目列表（异步版本）
     */
    private suspend fun getCategoryProjects(category: String): List<String> {
        return try {
            val config = projectDataManager.getConfigForCategory(category)
            val result = projectDataManager.loadProjects(config)

            when (result) {
                is ProjectDataManager.LoadResult.Success -> {
                    result.projects.map { it.id }
                }
                else -> {
                    Log.w(TAG, "获取分类 $category 项目列表失败")
                    emptyList()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取分类 $category 项目列表异常", e)
            emptyList()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()

        // 模块3：清理预加载任务
        if (::smartPreloadManager.isInitialized) {
            smartPreloadManager.cancelAllPreloads()
        }

        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        _binding = null
    }
}