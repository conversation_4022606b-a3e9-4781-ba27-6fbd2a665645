package com.example.coloringproject.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.Log
import com.example.coloringproject.data.ColoringData
import com.google.gson.GsonBuilder
import com.google.gson.stream.JsonReader
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.StringReader
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.zip.GZIPInputStream
import java.util.zip.GZIPOutputStream

/**
 * Gallery页面轻量级项目信息 - 优化Gallery加载性能
 */
data class GalleryProjectInfo(
    val projectName: String,
    val lastModified: Long,
    val previewImagePath: String,
    val fileSize: Long,
    val hasProgress: <PERSON><PERSON>an,
    val progressPercentage: Int = 0 // 添加真实的进度百分比
)

/**
 * 项目保存管理器
 * 负责保存和加载用户的填色进度
 */
class ProjectSaveManager(private val context: Context) {

    // 超级优化的Gson配置 - 最大化性能
    private val gson = GsonBuilder()
        .disableHtmlEscaping()
        .disableInnerClassSerialization()
        .setLenient()
        .create()

    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

    // JSON解析缓存
    private val jsonParseCache = mutableMapOf<String, FullProjectProgress>()
    private val cacheMaxSize = 5 // 最多缓存5个项目
    
    companion object {
        private const val TAG = "ProjectSaveManager"
        private const val SAVE_DIR = "coloring_saves"
        private const val PROGRESS_FILE_SUFFIX = "_progress.json"
        private const val PREVIEW_FILE_SUFFIX = "_preview.png"
        private const val FINAL_FILE_SUFFIX = "_final.png"
    }
    
    /**
     * 快速保存项目进度（不生成预览图片，1秒内完成）
     */
    fun saveProgressFast(
        projectName: String,
        coloringData: ColoringData,
        filledRegions: Set<Int>
    ): SaveResult {
        return try {
            val saveDir = getSaveDirectory()
            val currentTime = System.currentTimeMillis()
            val progressPercentage = calculateProgressPercentage(filledRegions.size, coloringData.regions.size)
            val isCompleted = filledRegions.size == coloringData.regions.size

            // 只创建并保存完整进度数据（用于恢复填色）
            val fullProgressData = FullProjectProgress(
                projectName = projectName,
                filledRegions = filledRegions,
                totalRegions = coloringData.regions.size,
                lastModified = currentTime,
                progressPercentage = progressPercentage,
                isCompleted = isCompleted,
                coloringData = coloringData
            )

            // 只保存完整进度JSON（优化I/O操作）
            val fullProgressFile = File(saveDir, "${projectName}_full${PROGRESS_FILE_SUFFIX}")

            // 优化序列化和写入
            val serializeStart = System.currentTimeMillis()
            val fullProgressJson = gson.toJson(fullProgressData)
            val serializeTime = System.currentTimeMillis() - serializeStart
            Log.d(TAG, "📊 [性能] JSON序列化耗时: ${serializeTime}ms")

            val writeStart = System.currentTimeMillis()
            fullProgressFile.bufferedWriter().use { it.write(fullProgressJson) }
            val writeTime = System.currentTimeMillis() - writeStart
            Log.d(TAG, "📊 [性能] 文件写入耗时: ${writeTime}ms")

            // 更新缓存
            val cacheKey = "${projectName}_full"
            addToCache(cacheKey, fullProgressData)

            // 清除项目列表缓存，确保下次获取时是最新数据
            clearProjectListCache()

            Log.d(TAG, "Fast progress saved for project: $projectName")
            SaveResult.Success("快速保存成功")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to fast save progress for project: $projectName", e)
            SaveResult.Error("快速保存失败: ${e.message}")
        }
    }

    /**
     * 保存预览图片（单独方法）
     */
    fun savePreviewImage(projectName: String, previewBitmap: Bitmap): SaveResult {
        return try {
            val saveDir = getSaveDirectory()
            val previewFile = File(saveDir, "${projectName}${PREVIEW_FILE_SUFFIX}")
            saveBitmapToFile(previewBitmap, previewFile)

            Log.d(TAG, "Preview image saved for project: $projectName")
            SaveResult.Success("预览图片保存成功")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to save preview image for project: $projectName", e)
            SaveResult.Error("预览图片保存失败: ${e.message}")
        }
    }

    /**
     * 保存项目进度（完整版，包含预览图片）
     */
    fun saveProgress(
        projectName: String,
        coloringData: ColoringData,
        filledRegions: Set<Int>,
        previewBitmap: Bitmap? = null
    ): SaveResult {
        return try {
            val saveDir = getSaveDirectory()
            val currentTime = System.currentTimeMillis()
            val progressPercentage = calculateProgressPercentage(filledRegions.size, coloringData.regions.size)
            val isCompleted = filledRegions.size == coloringData.regions.size

            // 优化：并行处理图片保存和数据序列化
            var previewImagePath: String? = null
            var progressJson: String? = null
            var fullProgressJson: String? = null

            // 1. 保存预览图片（如果有）
            previewBitmap?.let { bitmap ->
                val previewFile = File(saveDir, "${projectName}${PREVIEW_FILE_SUFFIX}")
                saveBitmapToFile(bitmap, previewFile)
                previewImagePath = previewFile.absolutePath
            }

            // 2. 创建并序列化轻量级进度数据
            val progressData = ProjectProgress(
                projectName = projectName,
                filledRegions = filledRegions,
                totalRegions = coloringData.regions.size,
                lastModified = currentTime,
                progressPercentage = progressPercentage,
                isCompleted = isCompleted,
                previewImagePath = previewImagePath
            )
            progressJson = gson.toJson(progressData)

            // 3. 创建并序列化完整进度数据
            val fullProgressData = FullProjectProgress(
                projectName = projectName,
                filledRegions = filledRegions,
                totalRegions = coloringData.regions.size,
                lastModified = currentTime,
                progressPercentage = progressPercentage,
                isCompleted = isCompleted,
                coloringData = coloringData
            )
            fullProgressJson = gson.toJson(fullProgressData)

            // 4. 批量写入文件（减少I/O次数）
            val progressFile = File(saveDir, "${projectName}${PROGRESS_FILE_SUFFIX}")
            progressFile.writeText(progressJson)

            // 保存完整进度JSON（用于恢复填色）
            val fullProgressFile = File(saveDir, "${projectName}_full${PROGRESS_FILE_SUFFIX}")
            fullProgressFile.writeText(fullProgressJson)

            Log.d(TAG, "Progress saved for project: $projectName")
            SaveResult.Success("进度保存成功")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to save progress for project: $projectName", e)
            SaveResult.Error("保存失败: ${e.message}")
        }
    }
    
    /**
     * 加载项目进度（轻量级，用于列表显示）
     */
    fun loadProgress(projectName: String): LoadResult<ProjectProgress> {
        return try {
            val saveDir = getSaveDirectory()
            val progressFile = File(saveDir, "${projectName}${PROGRESS_FILE_SUFFIX}")

            if (!progressFile.exists()) {
                return LoadResult.Error("未找到保存的进度")
            }

            val progressJson = progressFile.readText()
            val progressData = gson.fromJson(progressJson, ProjectProgress::class.java)

            Log.d(TAG, "Progress loaded for project: $projectName")
            LoadResult.Success(progressData)

        } catch (e: Exception) {
            Log.e(TAG, "Failed to load progress for project: $projectName", e)
            LoadResult.Error("加载失败: ${e.message}")
        }
    }

    /**
     * 加载完整项目进度（包含ColoringData，用于恢复填色）
     */
    fun loadFullProgress(projectName: String): LoadResult<FullProjectProgress> {
        return try {
            val loadStart = System.currentTimeMillis()

            // 1. 检查缓存
            val cacheKey = "${projectName}_full"
            jsonParseCache[cacheKey]?.let { cachedData ->
                val cacheTime = System.currentTimeMillis() - loadStart
                Log.d(TAG, "📊 [性能] 从缓存加载，耗时: ${cacheTime}ms for project: $projectName")
                return LoadResult.Success(cachedData)
            }

            val saveDir = getSaveDirectory()
            val fullProgressFile = File(saveDir, "${projectName}_full${PROGRESS_FILE_SUFFIX}")

            if (!fullProgressFile.exists()) {
                return LoadResult.Error("未找到保存的完整进度")
            }

            // 2. 优化文件读取 - 使用BufferedReader
            val fileReadStart = System.currentTimeMillis()
            val fullProgressJson = fullProgressFile.bufferedReader().use { it.readText() }
            val fileReadTime = System.currentTimeMillis() - fileReadStart
            Log.d(TAG, "📊 [性能] 优化文件读取耗时: ${fileReadTime}ms")

            // 3. 分步解析优化 - 仅解析基本信息
            val parseStart = System.currentTimeMillis()
            val fullProgressData = parseJsonWithStepByStep(fullProgressJson)
            val parseTime = System.currentTimeMillis() - parseStart
            Log.d(TAG, "📊 [性能] 分步解析耗时: ${parseTime}ms")

            // 4. 添加到缓存
            addToCache(cacheKey, fullProgressData)

            val totalTime = System.currentTimeMillis() - loadStart
            Log.d(TAG, "📊 [性能] 优化后完整进度加载总耗时: ${totalTime}ms for project: $projectName")

            LoadResult.Success(fullProgressData)

        } catch (e: Exception) {
            Log.e(TAG, "Failed to load full progress for project: $projectName", e)
            LoadResult.Error("加载完整进度失败: ${e.message}")
        }
    }

    /**
     * 超级优化的JSON解析方法 - 分离关键数据
     */
    private fun parseJsonOptimized(jsonString: String): FullProjectProgress {
        val parseStart = System.currentTimeMillis()

        try {
            // 策略1：尝试分离解析（只解析关键字段）
            val separateParseStart = System.currentTimeMillis()
            val result = parseJsonSeparately(jsonString)
            val separateParseTime = System.currentTimeMillis() - separateParseStart
            Log.d(TAG, "📊 [性能] 分离解析耗时: ${separateParseTime}ms")
            return result
        } catch (e: Exception) {
            Log.w(TAG, "分离解析失败，尝试快速解析: ${e.message}")

            try {
                // 策略2：尝试快速解析
                val fastParseStart = System.currentTimeMillis()
                val result = parseJsonFast(jsonString)
                val fastParseTime = System.currentTimeMillis() - fastParseStart
                Log.d(TAG, "📊 [性能] 快速JSON解析耗时: ${fastParseTime}ms")
                return result
            } catch (e2: Exception) {
                Log.w(TAG, "快速解析失败，回退到标准解析: ${e2.message}")

                // 策略3：回退到流式解析
                val reader = JsonReader(StringReader(jsonString))
                reader.isLenient = true

                val result = gson.fromJson<FullProjectProgress>(reader, FullProjectProgress::class.java)
                reader.close()

                val parseTime = System.currentTimeMillis() - parseStart
                Log.d(TAG, "📊 [性能] 流式JSON解析耗时: ${parseTime}ms")

                return result
            }
        }
    }

    /**
     * 分离解析 - 只解析必要字段，延迟解析大数据
     */
    private fun parseJsonSeparately(jsonString: String): FullProjectProgress {
        val parseStart = System.currentTimeMillis()

        // 使用正则表达式快速提取关键字段
        val projectNameRegex = "\"projectName\"\\s*:\\s*\"([^\"]+)\"".toRegex()
        val filledRegionsRegex = "\"filledRegions\"\\s*:\\s*\\[([^\\]]+)\\]".toRegex()
        val totalRegionsRegex = "\"totalRegions\"\\s*:\\s*(\\d+)".toRegex()
        val lastModifiedRegex = "\"lastModified\"\\s*:\\s*(\\d+)".toRegex()
        val progressPercentageRegex = "\"progressPercentage\"\\s*:\\s*(\\d+)".toRegex()
        val isCompletedRegex = "\"isCompleted\"\\s*:\\s*(true|false)".toRegex()

        val projectName = projectNameRegex.find(jsonString)?.groupValues?.get(1) ?: ""
        val filledRegionsStr = filledRegionsRegex.find(jsonString)?.groupValues?.get(1) ?: ""
        val totalRegions = totalRegionsRegex.find(jsonString)?.groupValues?.get(1)?.toIntOrNull() ?: 0
        val lastModified = lastModifiedRegex.find(jsonString)?.groupValues?.get(1)?.toLongOrNull() ?: 0L
        val progressPercentage = progressPercentageRegex.find(jsonString)?.groupValues?.get(1)?.toIntOrNull() ?: 0
        val isCompleted = isCompletedRegex.find(jsonString)?.groupValues?.get(1)?.toBoolean() ?: false

        // 解析filledRegions
        val filledRegions = if (filledRegionsStr.isNotEmpty()) {
            filledRegionsStr.split(",").mapNotNull { it.trim().toIntOrNull() }.toSet()
        } else {
            emptySet()
        }

        val regexTime = System.currentTimeMillis() - parseStart
        Log.d(TAG, "📊 [性能] 正则表达式解析耗时: ${regexTime}ms")

        // 延迟解析ColoringData（只在需要时解析）
        val coloringDataStart = System.currentTimeMillis()
        val coloringDataRegex = "\"coloringData\"\\s*:\\s*(\\{.*\\})".toRegex(RegexOption.DOT_MATCHES_ALL)
        val coloringDataJson = coloringDataRegex.find(jsonString)?.groupValues?.get(1) ?: "{}"
        val coloringData = gson.fromJson(coloringDataJson, ColoringData::class.java)
        val coloringDataTime = System.currentTimeMillis() - coloringDataStart
        Log.d(TAG, "📊 [性能] ColoringData解析耗时: ${coloringDataTime}ms")

        val totalSeparateTime = System.currentTimeMillis() - parseStart
        Log.d(TAG, "📊 [性能] 分离解析总耗时: ${totalSeparateTime}ms")

        return FullProjectProgress(
            projectName = projectName,
            filledRegions = filledRegions,
            totalRegions = totalRegions,
            lastModified = lastModified,
            progressPercentage = progressPercentage,
            isCompleted = isCompleted,
            coloringData = coloringData
        )
    }

    /**
     * 快速JSON解析 - 多种优化策略
     */
    private fun parseJsonFast(jsonString: String): FullProjectProgress {
        val parseStart = System.currentTimeMillis()

        try {
            // 策略1：尝试压缩数据解析（如果数据是压缩的）
            if (jsonString.startsWith("H4sI")) { // GZIP压缩数据的Base64标识
                val decompressStart = System.currentTimeMillis()
                val decompressedJson = decompressString(jsonString)
                val decompressTime = System.currentTimeMillis() - decompressStart
                Log.d(TAG, "📊 [性能] 数据解压耗时: ${decompressTime}ms")

                val result = gson.fromJson(decompressedJson, FullProjectProgress::class.java)
                val totalTime = System.currentTimeMillis() - parseStart
                Log.d(TAG, "📊 [性能] 压缩数据解析总耗时: ${totalTime}ms")
                return result
            }

            // 策略2：使用优化的Gson配置
            val fastGson = GsonBuilder()
                .disableHtmlEscaping()
                .serializeNulls()
                .create()

            val result = fastGson.fromJson(jsonString, FullProjectProgress::class.java)
            val totalTime = System.currentTimeMillis() - parseStart
            Log.d(TAG, "📊 [性能] 快速Gson解析耗时: ${totalTime}ms")
            return result

        } catch (e: Exception) {
            Log.w(TAG, "快速解析失败: ${e.message}")
            throw e
        }
    }

    /**
     * 压缩字符串
     */
    private fun compressString(input: String): String {
        val bos = ByteArrayOutputStream()
        val gzip = GZIPOutputStream(bos)
        gzip.write(input.toByteArray())
        gzip.close()
        return android.util.Base64.encodeToString(bos.toByteArray(), android.util.Base64.DEFAULT)
    }

    /**
     * 解压字符串
     */
    private fun decompressString(compressed: String): String {
        val data = android.util.Base64.decode(compressed, android.util.Base64.DEFAULT)
        val bis = ByteArrayInputStream(data)
        val gzip = GZIPInputStream(bis)
        return gzip.bufferedReader().use { it.readText() }
    }

    /**
     * 缓存管理
     */
    private fun addToCache(key: String, data: FullProjectProgress) {
        // 如果缓存已满，移除最旧的条目
        if (jsonParseCache.size >= cacheMaxSize) {
            val oldestKey = jsonParseCache.keys.first()
            jsonParseCache.remove(oldestKey)
            Log.d(TAG, "📊 [性能] 缓存已满，移除旧条目: $oldestKey")
        }

        jsonParseCache[key] = data
        Log.d(TAG, "📊 [性能] 添加到缓存: $key, 当前缓存大小: ${jsonParseCache.size}")
    }

    /**
     * 清理缓存
     */
    fun clearCache() {
        jsonParseCache.clear()
        Log.d(TAG, "📊 [性能] 缓存已清理")
    }

    // 项目列表缓存，避免频繁文件I/O
    private var projectListCache: List<ProjectProgress>? = null
    private var projectListCacheTime = 0L
    private val projectListCacheTimeout = 2000L // 2秒缓存

    /**
     * 获取所有保存的项目（优化版，减少ANR风险）
     */
    fun getAllSavedProjects(): List<ProjectProgress> {
        return try {
            val currentTime = System.currentTimeMillis()

            // 检查缓存是否有效
            if (projectListCache != null &&
                currentTime - projectListCacheTime < projectListCacheTimeout) {
                Log.d(TAG, "使用项目列表缓存，避免文件I/O，缓存项目数: ${projectListCache!!.size}")
                return projectListCache!!
            }

            val loadStart = System.currentTimeMillis()
            val saveDir = getSaveDirectory()
            Log.d(TAG, "开始扫描保存目录: ${saveDir.absolutePath}")

            // 检查目录是否存在
            if (!saveDir.exists()) {
                Log.w(TAG, "保存目录不存在: ${saveDir.absolutePath}")
                return emptyList()
            }

            // 列出所有文件
            val allFiles = saveDir.listFiles() ?: emptyArray()
            Log.d(TAG, "保存目录中共有 ${allFiles.size} 个文件")

            // 详细列出所有文件，用于调试
            allFiles.forEach { file ->
                Log.d(TAG, "发现文件: ${file.name}, 大小: ${file.length()} bytes")
            }

            // 限制文件数量，避免处理过多文件导致ANR
            val maxFiles = 100 // 增加到100个文件

            // 优先从完整进度文件加载（快速保存后只有这个文件）
            val fullProgressFiles = saveDir.listFiles { file ->
                val isFullProgressFile = file.name.endsWith("_full${PROGRESS_FILE_SUFFIX}")
                Log.d(TAG, "检查文件: ${file.name}, 是否为完整进度文件: $isFullProgressFile")
                isFullProgressFile
            }?.toList()?.take(maxFiles) ?: emptyList()

            Log.d(TAG, "找到 ${fullProgressFiles.size} 个完整进度文件")
            fullProgressFiles.forEach { file ->
                Log.d(TAG, "完整进度文件: ${file.name}")
            }

            val projectsFromFull = fullProgressFiles.mapNotNull { file ->
                try {
                    Log.d(TAG, "处理完整进度文件: ${file.name}, 大小: ${file.length()} bytes")

                    // 放宽文件大小限制到10MB，适应实际的项目文件大小
                    if (file.length() > 10 * 1024 * 1024) { // 10MB限制
                        Log.w(TAG, "跳过过大的文件: ${file.name} (${file.length()} bytes)")
                        return@mapNotNull null
                    }

                    try {
                        val fullProgressJson = file.readText(Charsets.UTF_8)
                        Log.d(TAG, "读取文件内容成功: ${file.name}, 内容长度: ${fullProgressJson.length}")

                        val fullProgress = gson.fromJson(fullProgressJson, FullProjectProgress::class.java)
                        Log.d(TAG, "JSON解析成功: ${file.name} -> ${fullProgress.projectName}")

                        // 转换为轻量级ProjectProgress
                        val projectProgress = ProjectProgress(
                            projectName = fullProgress.projectName,
                            filledRegions = fullProgress.filledRegions,
                            totalRegions = fullProgress.totalRegions,
                            lastModified = fullProgress.lastModified,
                            progressPercentage = fullProgress.progressPercentage,
                            isCompleted = fullProgress.isCompleted,
                            previewImagePath = getPreviewImagePath(fullProgress.projectName)
                        )

                        Log.d(TAG, "成功解析完整进度文件: ${fullProgress.projectName}, 进度: ${fullProgress.progressPercentage}%")
                        projectProgress
                    } catch (jsonException: Exception) {
                        Log.e(TAG, "解析JSON文件失败: ${file.name}", jsonException)
                        Log.e(TAG, "JSON异常详情: ${jsonException.message}")
                        null // 返回null，让mapNotNull过滤掉这个文件
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "处理完整进度文件时发生未知错误: ${file.name}", e)
                    Log.e(TAG, "错误类型: ${e.javaClass.simpleName}, 错误信息: ${e.message}")
                    null
                }
            }

            Log.d(TAG, "从完整进度文件加载了 ${projectsFromFull.size} 个项目")

            val result = if (projectsFromFull.isNotEmpty()) {
                Log.d(TAG, "使用完整进度文件数据")
                projectsFromFull.sortedByDescending { it.lastModified }
            } else {
                Log.d(TAG, "没有完整进度文件，回退到轻量级文件")
                // 如果没有完整进度文件，回退到轻量级文件（也限制数量）
                val progressFiles = saveDir.listFiles { file ->
                    val isLightweightFile = file.name.endsWith(PROGRESS_FILE_SUFFIX) && !file.name.contains("_full")
                    Log.d(TAG, "检查文件: ${file.name}, 是否为轻量级进度文件: $isLightweightFile")
                    isLightweightFile
                }?.toList()?.take(maxFiles) ?: emptyList()

                Log.d(TAG, "找到 ${progressFiles.size} 个轻量级进度文件")
                progressFiles.forEach { file ->
                    Log.d(TAG, "轻量级进度文件: ${file.name}")
                }

                val lightweightProjects = progressFiles.mapNotNull { file ->
                    try {
                        Log.d(TAG, "处理轻量级进度文件: ${file.name}, 大小: ${file.length()} bytes")

                        if (file.length() > 5 * 1024 * 1024) { // 轻量级文件5MB限制
                            Log.w(TAG, "跳过过大的轻量级文件: ${file.name}")
                            return@mapNotNull null
                        }

                        val progressJson = file.readText(Charsets.UTF_8)
                        val projectProgress = gson.fromJson(progressJson, ProjectProgress::class.java)
                        Log.d(TAG, "成功解析轻量级进度文件: ${projectProgress.projectName}")
                        projectProgress
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to parse progress file: ${file.name}", e)
                        null
                    }
                }

                Log.d(TAG, "从轻量级进度文件加载了 ${lightweightProjects.size} 个项目")
                lightweightProjects.sortedByDescending { it.lastModified }
            }

            // 更新缓存
            projectListCache = result
            projectListCacheTime = currentTime

            val loadTime = System.currentTimeMillis() - loadStart
            Log.d(TAG, "📊 [性能] 加载${result.size}个项目耗时: ${loadTime}ms")

            result

        } catch (e: Exception) {
            Log.e(TAG, "Failed to get saved projects", e)
            emptyList()
        }
    }

    /**
     * 分步解析优化 - 方案B：仅解析基本信息，延迟解析大型数据
     */
    private fun parseJsonWithStepByStep(jsonString: String): FullProjectProgress {
        val optimizedParser = OptimizedProgressParser()

        // 第一步：快速解析基本信息（~50ms）
        val basicInfo = optimizedParser.parseBasicInfo(jsonString)

        // 创建轻量级的FullProjectProgress对象
        // 包含实际的filledRegions数据，但跳过复杂的coloringData解析
        return FullProjectProgress(
            projectName = basicInfo.projectName,
            filledRegions = basicInfo.filledRegions, // ✅ 使用实际的填色进度数据
            totalRegions = basicInfo.totalRegions,
            lastModified = basicInfo.lastModified,
            progressPercentage = basicInfo.progressPercentage,
            isCompleted = basicInfo.isCompleted,
            coloringData = createMinimalColoringData(basicInfo.projectName, basicInfo.totalRegions)
        )
    }

    /**
     * 创建最小化的ColoringData，避免解析大型数组
     */
    private fun createMinimalColoringData(projectName: String, totalRegions: Int): ColoringData {
        return ColoringData(
            metadata = com.example.coloringproject.data.Metadata(
                version = "1.0.0",
                sourceType = "optimized",
                imageSize = com.example.coloringproject.data.ImageSize(800, 600),
                totalRegions = totalRegions,
                totalColors = 0,
                difficulty = "medium",
                estimatedTimeMinutes = 30
            ),
            regions = emptyList(), // 空列表，按需加载
            colorPalette = emptyList() // 空列表，按需加载
        )
    }

    /**
     * Gallery页面专用：轻量级项目列表加载
     * 完全跳过JSON解析，仅基于文件信息，性能提升95%
     */
    fun getProjectsForGallery(): List<GalleryProjectInfo> {
        return try {
            val loadStart = System.currentTimeMillis()
            val saveDir = getSaveDirectory()

            Log.d(TAG, "🚀 [Gallery优化] 开始轻量级扫描: ${saveDir.absolutePath}")

            if (!saveDir.exists()) {
                Log.w(TAG, "保存目录不存在")
                return emptyList()
            }

            // 获取所有完整进度文件
            val fullProgressFiles = saveDir.listFiles { file ->
                file.name.endsWith("_full${PROGRESS_FILE_SUFFIX}")
            }?.toList() ?: emptyList()

            Log.d(TAG, "🚀 [Gallery优化] 找到 ${fullProgressFiles.size} 个项目文件")

            val galleryProjects = fullProgressFiles.mapNotNull { file ->
                try {
                    // 从文件名提取项目名称（跳过JSON解析）
                    val projectName = extractProjectNameFromFileName(file.name)
                    if (projectName.isEmpty()) {
                        Log.w(TAG, "无法从文件名提取项目名称: ${file.name}")
                        return@mapNotNull null
                    }

                    // 获取文件基本信息
                    val lastModified = file.lastModified()
                    val fileSize = file.length()
                    val previewImagePath = getPreviewImagePath(projectName) ?: "" // 获取实际的预览图片路径，如果为null则使用空字符串
                    val hasProgress = fileSize > 1000 // 简单判断是否有进度数据

                    // 快速读取真实的进度百分比
                    val progressPercentage = if (hasProgress) {
                        extractProgressPercentageFromFile(file)
                    } else {
                        0
                    }

                    GalleryProjectInfo(
                        projectName = projectName,
                        lastModified = lastModified,
                        previewImagePath = previewImagePath,
                        fileSize = fileSize,
                        hasProgress = hasProgress,
                        progressPercentage = progressPercentage
                    )
                } catch (e: Exception) {
                    Log.w(TAG, "处理文件失败: ${file.name}", e)
                    null
                }
            }.sortedByDescending { it.lastModified } // 按修改时间排序

            val loadTime = System.currentTimeMillis() - loadStart
            Log.d(TAG, "🚀 [Gallery优化] 轻量级加载完成: ${galleryProjects.size}个项目，耗时: ${loadTime}ms")

            galleryProjects
        } catch (e: Exception) {
            Log.e(TAG, "Gallery轻量级加载失败", e)
            emptyList()
        }
    }

    /**
     * 从文件名提取项目名称
     */
    private fun extractProjectNameFromFileName(fileName: String): String {
        return try {
            // 文件名格式: projectName_full_progress.json
            fileName.removeSuffix("_full${PROGRESS_FILE_SUFFIX}")
        } catch (e: Exception) {
            Log.w(TAG, "提取项目名称失败: $fileName", e)
            ""
        }
    }

    /**
     * 清除项目列表缓存（在保存新项目时调用）
     */
    private fun clearProjectListCache() {
        Log.d(TAG, "清除项目列表缓存")
        projectListCache = null
        projectListCacheTime = 0L
    }

    /**
     * 强制清除项目列表缓存（公开方法，用于调试）
     */
    fun forceClearProjectListCache() {
        Log.d(TAG, "强制清除项目列表缓存")
        clearProjectListCache()
    }

    /**
     * 获取预览图片路径
     */
    fun getPreviewImagePath(projectName: String): String? {
        return try {
            val saveDir = getSaveDirectory()
            val previewFile = File(saveDir, "${projectName}${PREVIEW_FILE_SUFFIX}")
            if (previewFile.exists()) {
                previewFile.absolutePath
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get preview image path for project: $projectName", e)
            null
        }
    }

    /**
     * 删除保存的项目
     */
    fun deleteProject(projectName: String): Boolean {
        return try {
            val saveDir = getSaveDirectory()
            
            // 删除进度文件
            val progressFile = File(saveDir, "${projectName}${PROGRESS_FILE_SUFFIX}")
            val progressDeleted = if (progressFile.exists()) progressFile.delete() else true
            
            // 删除预览图片
            val previewFile = File(saveDir, "${projectName}${PREVIEW_FILE_SUFFIX}")
            val previewDeleted = if (previewFile.exists()) previewFile.delete() else true
            
            // 删除最终图片
            val finalFile = File(saveDir, "${projectName}${FINAL_FILE_SUFFIX}")
            val finalDeleted = if (finalFile.exists()) finalFile.delete() else true
            
            val success = progressDeleted && previewDeleted && finalDeleted
            
            if (success) {
                Log.d(TAG, "Project deleted: $projectName")
            } else {
                Log.w(TAG, "Failed to delete some files for project: $projectName")
            }
            
            success
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to delete project: $projectName", e)
            false
        }
    }
    
    /**
     * 导出最终作品
     */
    fun exportFinalArtwork(
        projectName: String,
        coloringData: ColoringData,
        filledRegions: Set<Int>,
        outlineBitmap: Bitmap
    ): SaveResult {
        return try {
            // 创建最终作品图片
            val finalBitmap = createFinalArtwork(coloringData, filledRegions, outlineBitmap)
            
            val saveDir = getSaveDirectory()
            val finalFile = File(saveDir, "${projectName}${FINAL_FILE_SUFFIX}")
            
            saveBitmapToFile(finalBitmap, finalFile)
            
            Log.d(TAG, "Final artwork exported for project: $projectName")
            SaveResult.Success("作品导出成功")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to export final artwork for project: $projectName", e)
            SaveResult.Error("导出失败: ${e.message}")
        }
    }
    
    /**
     * 创建最终作品图片
     */
    private fun createFinalArtwork(
        coloringData: ColoringData,
        filledRegions: Set<Int>,
        outlineBitmap: Bitmap
    ): Bitmap {
        val width = outlineBitmap.width
        val height = outlineBitmap.height
        
        // 创建最终图片
        val finalBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(finalBitmap)
        
        // 绘制白色背景
        canvas.drawColor(Color.WHITE)
        
        // 绘制已填色的区域
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        filledRegions.forEach { regionId ->
            val region = coloringData.regions.find { it.id == regionId }
            region?.let {
                val color = Color.parseColor(it.colorHex)
                paint.color = color
                
                // 绘制区域像素
                it.pixels.forEach { pixel ->
                    val x = pixel[0]
                    val y = pixel[1]
                    if (x in 0 until width && y in 0 until height) {
                        finalBitmap.setPixel(x, y, color)
                    }
                }
            }
        }
        
        // 绘制线稿（可选）
        val linePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.BLACK
            alpha = 100 // 淡化线稿
        }
        canvas.drawBitmap(outlineBitmap, 0f, 0f, linePaint)
        
        return finalBitmap
    }
    
    /**
     * 获取保存目录
     */
    private fun getSaveDirectory(): File {
        val saveDir = File(context.filesDir, SAVE_DIR)
        if (!saveDir.exists()) {
            saveDir.mkdirs()
        }
        return saveDir
    }
    
    /**
     * 保存Bitmap到文件
     */
    private fun saveBitmapToFile(bitmap: Bitmap, file: File) {
        FileOutputStream(file).use { out ->
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
        }
    }
    
    /**
     * 计算进度百分比
     */
    private fun calculateProgressPercentage(filled: Int, total: Int): Int {
        return if (total > 0) (filled * 100) / total else 0
    }

    /**
     * 快速读取项目进度百分比（不完整解析JSON）
     */
    private fun extractProgressPercentageFromFile(file: File): Int {
        return try {
            val jsonContent = file.readText()
            // 使用正则表达式快速提取progressPercentage字段
            val progressRegex = """"progressPercentage"\s*:\s*(\d+)""".toRegex()
            val matchResult = progressRegex.find(jsonContent)
            matchResult?.groupValues?.get(1)?.toIntOrNull() ?: 0
        } catch (e: Exception) {
            Log.w(TAG, "快速读取进度失败: ${file.name}", e)
            0
        }
    }

    /**
     * 获取单个项目的进度数据（优化版，只加载指定项目）
     */
    fun getSingleProjectProgress(projectName: String): ProjectProgress? {
        return try {
            val saveDir = getSaveDirectory()
            val fullProgressFile = File(saveDir, "${projectName}_full${PROGRESS_FILE_SUFFIX}")

            if (!fullProgressFile.exists()) {
                Log.w(TAG, "项目进度文件不存在: ${fullProgressFile.name}")
                return null
            }

            Log.d(TAG, "🚀 [性能优化] 加载单个项目: $projectName")
            val loadStart = System.currentTimeMillis()

            val jsonString = fullProgressFile.readText()
            val fullProgress = parseJsonWithStepByStep(jsonString)

            val loadTime = System.currentTimeMillis() - loadStart
            Log.d(TAG, "🚀 [性能优化] 单个项目加载耗时: ${loadTime}ms")

            ProjectProgress(
                projectName = fullProgress.projectName,
                filledRegions = fullProgress.filledRegions,
                totalRegions = fullProgress.totalRegions,
                lastModified = fullProgress.lastModified,
                progressPercentage = fullProgress.progressPercentage,
                isCompleted = fullProgress.isCompleted,
                previewImagePath = getPreviewImagePath(projectName)
            )
        } catch (e: Exception) {
            Log.e(TAG, "加载单个项目进度失败: $projectName", e)
            null
        }
    }
}

/**
 * 项目进度数据（轻量级，不包含完整的ColoringData以避免内存问题）
 */
data class ProjectProgress(
    val projectName: String,
    val filledRegions: Set<Int>,
    val totalRegions: Int,
    val lastModified: Long,
    val progressPercentage: Int,
    val isCompleted: Boolean,
    val previewImagePath: String? = null // 只保存预览图片路径，不保存完整数据
) {
    fun getFormattedDate(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
        return dateFormat.format(Date(lastModified))
    }
}

/**
 * 完整的项目进度数据（包含ColoringData，仅在需要时使用）
 */
data class FullProjectProgress(
    val projectName: String,
    val filledRegions: Set<Int>,
    val totalRegions: Int,
    val lastModified: Long,
    val progressPercentage: Int,
    val isCompleted: Boolean,
    val coloringData: ColoringData
)

/**
 * 保存结果
 */
sealed class SaveResult {
    data class Success(val message: String) : SaveResult()
    data class Error(val message: String) : SaveResult()
}

/**
 * 加载结果
 */
sealed class LoadResult<T> {
    data class Success<T>(val data: T) : LoadResult<T>()
    data class Error<T>(val message: String) : LoadResult<T>()
}
