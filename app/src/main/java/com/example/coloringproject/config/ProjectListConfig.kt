package com.example.coloringproject.config

import com.example.coloringproject.utils.HybridResourceManager
import com.example.coloringproject.manager.CategoryMappingManager

/**
 * 项目列表配置类
 * 定义不同类型项目列表的配置参数
 */
data class ProjectListConfig(
    /**
     * 项目类型标识
     */
    val type: ProjectType,
    
    /**
     * 显示标题
     */
    val title: String,
    
    /**
     * 是否启用分类功能
     */
    val enableCategories: Boolean = true,
    
    /**
     * 是否启用搜索功能
     */
    val enableSearch: Boolean = true,
    
    /**
     * 是否启用随机项目功能
     */
    val enableRandomProject: Boolean = true,
    
    /**
     * 默认分类
     */
    val defaultCategory: String = "New",
    
    /**
     * 项目过滤器
     */
    val projectFilter: ((HybridResourceManager.HybridProject) -> Boolean)? = null,
    
    /**
     * 网格列数
     */
    val spanCount: Int = 2,
    
    /**
     * 是否启用下拉刷新
     */
    val enableSwipeRefresh: Boolean = true,
    
    /**
     * 缓存策略
     */
    val cacheStrategy: CacheStrategy = CacheStrategy.NORMAL
) {
    
    /**
     * 项目类型枚举
     */
    enum class ProjectType(val id: String, val displayName: String) {
        NEW("new", "新项目"),
        ANIMAL("animal", "动物"),
        BUILDING("building", "建筑"),
        CASTLE("castle", "城堡"),
        GARDENS("gardens", "花园"),
        TREEHOUSE("treehouse", "树屋"),
        ALL("all", "全部");

        companion object {
            /**
             * 根据ID查找类型，如果不存在则创建动态类型
             */
            fun fromId(id: String): ProjectType {
                return values().find { it.id == id } ?: createDynamicType(id)
            }

            /**
             * 创建动态类型（用于运行时发现的新分类）
             */
            private fun createDynamicType(id: String): ProjectType {
                // 这里返回一个默认类型，实际的动态处理在配置创建时进行
                return ALL
            }
        }
    }
    
    /**
     * 缓存策略枚举
     */
    enum class CacheStrategy {
        NONE,       // 不缓存
        NORMAL,     // 正常缓存
        AGGRESSIVE  // 积极缓存
    }
    
    companion object {
        /**
         * 创建新项目列表配置
         */
        fun createNewProjectsConfig(): ProjectListConfig {
            return ProjectListConfig(
                type = ProjectType.NEW,
                title = "新项目",
                enableCategories = true,
                enableSearch = true,
                enableRandomProject = true,
                defaultCategory = "New"
            )
        }
        
        /**
         * 创建动物项目列表配置
         */
        fun createAnimalProjectsConfig(): ProjectListConfig {
            return ProjectListConfig(
                type = ProjectType.ANIMAL,
                title = "动物涂色",
                enableCategories = false,
                enableSearch = true,
                enableRandomProject = true,
                projectFilter = { project ->
                    project.category.contains("animal", ignoreCase = true) ||
                    project.id.contains("animal", ignoreCase = true)
                }
            )
        }
        
        /**
         * 创建建筑项目列表配置
         */
        fun createBuildingProjectsConfig(): ProjectListConfig {
            return ProjectListConfig(
                type = ProjectType.BUILDING,
                title = "建筑涂色",
                enableCategories = false,
                enableSearch = true,
                enableRandomProject = true,
                projectFilter = { project ->
                    project.category.contains("building", ignoreCase = true) ||
                    project.id.contains("building", ignoreCase = true)
                }
            )
        }
        
        /**
         * 创建花园项目列表配置
         */
        fun createGardensProjectsConfig(): ProjectListConfig {
            return ProjectListConfig(
                type = ProjectType.GARDENS,
                title = "花园涂色",
                enableCategories = false,
                enableSearch = true,
                enableRandomProject = true,
                projectFilter = { project ->
                    project.category.contains("gardens", ignoreCase = true) ||
                    project.id.contains("gardens", ignoreCase = true)
                }
            )
        }

        /**
         * 根据分类名称动态创建配置
         */
        fun createDynamicCategoryConfig(categoryName: String): ProjectListConfig {
            val displayName = formatCategoryDisplayName(categoryName)

            return ProjectListConfig(
                type = ProjectType.fromId(categoryName),
                title = "${displayName}涂色",
                enableCategories = false, // 分类页面不需要再显示分类标签
                enableSearch = true,
                enableRandomProject = true,
                projectFilter = { project ->
                    project.category.equals(categoryName, ignoreCase = true)
                }
            )
        }

        /**
         * 批量创建所有分类的配置
         */
        fun createConfigsForCategories(categories: List<String>): Map<String, ProjectListConfig> {
            val configs = mutableMapOf<String, ProjectListConfig>()

            for (category in categories) {
                configs[category] = createDynamicCategoryConfig(category)
            }

            return configs
        }

        /**
         * 格式化分类显示名称
         */
        private fun formatCategoryDisplayName(categoryName: String): String {
            return CategoryMappingManager.getCategoryDisplayName(categoryName)
        }
    }
}
