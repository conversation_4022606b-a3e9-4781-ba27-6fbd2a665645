package com.example.coloringproject.manager

import android.content.Context
import android.util.Log
import com.example.coloringproject.config.ProjectListConfig
import com.example.coloringproject.utils.HybridResourceManager
import com.example.coloringproject.utils.LightweightResourceValidator
import com.example.coloringproject.utils.CategoryManager
import com.example.coloringproject.manager.CategoryMappingManager
import com.example.coloringproject.network.ResourceDownloadManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 项目数据管理器
 * 统一管理不同类型的项目数据加载和缓存
 */
class ProjectDataManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "ProjectDataManager"
        
        @Volatile
        private var INSTANCE: ProjectDataManager? = null
        
        fun getInstance(context: Context): ProjectDataManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ProjectDataManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    // 数据加载器
    private val lightweightValidator = LightweightResourceValidator(context)
    private val resourceDownloadManager = ResourceDownloadManager(context)
    
    // 缓存存储
    private val projectCache = mutableMapOf<String, List<LightweightResourceValidator.LightweightProject>>()
    private val categoryCache = mutableMapOf<String, List<CategoryManager.Category>>()
    
    /**
     * 数据加载结果
     */
    sealed class LoadResult {
        data class Success(val projects: List<LightweightResourceValidator.LightweightProject>) : LoadResult()
        data class Error(val message: String, val exception: Throwable? = null) : LoadResult()
        object Loading : LoadResult()
    }
    
    /**
     * 混合获取分类列表（assets + 服务器）
     * 优先加载assets分类，然后补充服务器分类
     */
    suspend fun getAvailableCategories(): Result<List<String>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始混合获取分类列表")

            // 1. 获取assets分类
            val assetsCategories = getAssetsCategories()
            Log.d(TAG, "Assets分类: ${assetsCategories.joinToString(", ")}")

            // 2. 获取服务器分类
            val serverCategories = getServerCategories()
            Log.d(TAG, "服务器分类: ${serverCategories.joinToString(", ")}")

            // 3. 合并分类列表
            val mergedCategories = CategoryMappingManager.mergeCategories(assetsCategories, serverCategories)
            Log.d(TAG, "合并后分类: ${mergedCategories.joinToString(", ")}")

            Result.success(mergedCategories)
        } catch (e: Exception) {
            Log.e(TAG, "混合获取分类失败", e)
            // 如果混合获取失败，至少返回assets分类
            try {
                val assetsCategories = getAssetsCategories()
                Log.w(TAG, "降级到仅assets分类: ${assetsCategories.joinToString(", ")}")
                Result.success(assetsCategories)
            } catch (fallbackException: Exception) {
                Log.e(TAG, "连assets分类都获取失败", fallbackException)
                Result.failure(e)
            }
        }
    }

    /**
     * 优先获取本地分类，网络分类异步补充
     * 解决启动时等待网络请求的问题
     */
    suspend fun getAvailableCategoriesWithPriority(
        onNetworkCategoriesLoaded: (List<String>) -> Unit
    ): Result<List<String>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始优先获取分类列表")

            // 1. 立即获取assets分类
            val assetsCategories = getAssetsCategories()
            Log.d(TAG, "Assets分类加载完成: ${assetsCategories.joinToString(", ")}")

            // 2. 异步获取服务器分类
            kotlinx.coroutines.GlobalScope.launch(Dispatchers.IO) {
                try {
                    val serverCategories = getServerCategories()
                    if (serverCategories.isNotEmpty()) {
                        Log.d(TAG, "服务器分类加载完成: ${serverCategories.joinToString(", ")}")
                        
                        // 合并分类并回调
                        val mergedCategories = CategoryMappingManager.mergeCategories(assetsCategories, serverCategories)
                        val newCategories = mergedCategories.filter { it !in assetsCategories }
                        
                        if (newCategories.isNotEmpty()) {
                            Log.d(TAG, "新增服务器分类: ${newCategories.joinToString(", ")}")
                            onNetworkCategoriesLoaded(newCategories)
                        } else {
                            Log.d(TAG, "没有新的服务器分类需要添加")
                        }
                    } else {
                        Log.d(TAG, "没有服务器分类")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "异步获取服务器分类失败", e)
                    // 网络分类加载失败不影响本地分类的显示
                }
            }

            // 3. 立即返回本地分类
            Result.success(assetsCategories)
        } catch (e: Exception) {
            Log.e(TAG, "优先获取分类失败", e)
            Result.failure(e)
        }
    }

    /**
     * 获取assets中的分类文件夹
     */
    private suspend fun getAssetsCategories(): List<String> = withContext(Dispatchers.IO) {
        try {
            val assetManager = context.assets
            val categories = mutableListOf<String>()

            // 获取assets根目录下的所有文件夹
            val assetFiles = assetManager.list("") ?: emptyArray()

            for (file in assetFiles) {
                try {
                    // 检查是否是文件夹（通过尝试列出子文件）
                    val subFiles = assetManager.list(file)
                    Log.d(TAG, "检查文件/文件夹: $file, 子文件: ${subFiles?.joinToString(", ") ?: "null"}")
                    if (subFiles != null && subFiles.isNotEmpty()) {
                        // 检查文件夹中是否包含JSON文件（确认是项目分类文件夹）
                        val hasJsonFiles = subFiles.any { it.endsWith(".json") }
                        Log.d(TAG, "文件夹 $file 包含JSON文件: $hasJsonFiles")
                        if (hasJsonFiles) {
                            categories.add(file)
                            Log.d(TAG, "✅ 发现分类文件夹: $file (包含${subFiles.size}个文件)")
                        } else {
                            Log.d(TAG, "❌ 跳过文件夹 $file (无JSON文件)")
                        }
                    }
                } catch (e: Exception) {
                    // 如果无法列出子文件，说明这是一个文件而不是文件夹
                    Log.d(TAG, "跳过文件: $file (异常: ${e.message})")
                }
            }

            Log.d(TAG, "总共发现 ${categories.size} 个assets分类: ${categories.joinToString(", ")}")
            categories.sorted() // 按字母顺序排序
        } catch (e: Exception) {
            Log.e(TAG, "获取assets分类失败", e)
            emptyList()
        }
    }

    /**
     * 获取服务器分类列表
     */
    private suspend fun getServerCategories(): List<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始获取服务器分类")
            val result = resourceDownloadManager.getCategoriesList()

            if (result.isSuccess) {
                val apiResponse = result.getOrNull()
                if (apiResponse?.status == "success" && apiResponse.data != null) {
                    val serverCategoryNames = apiResponse.data.categories.map { it.name }
                    Log.d(TAG, "成功获取服务器分类: ${serverCategoryNames.joinToString(", ")}")
                    return@withContext serverCategoryNames
                } else {
                    Log.w(TAG, "服务器分类API返回失败状态: ${apiResponse?.status}")
                }
            } else {
                Log.w(TAG, "服务器分类API调用失败: ${result.exceptionOrNull()?.message}")
            }

            emptyList<String>()
        } catch (e: Exception) {
            Log.e(TAG, "获取服务器分类异常", e)
            emptyList()
        }
    }

    /**
     * 根据配置加载项目数据
     */
    suspend fun loadProjects(config: ProjectListConfig): LoadResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始加载项目数据: ${config.type.displayName}")

            // 检查缓存
            val cacheKey = config.type.id
            if (config.cacheStrategy != ProjectListConfig.CacheStrategy.NONE) {
                projectCache[cacheKey]?.let { cachedProjects ->
                    Log.d(TAG, "使用缓存数据: ${cachedProjects.size}个项目")
                    return@withContext LoadResult.Success(filterProjects(cachedProjects, config))
                }
            }

            // 根据类型加载数据
            val allProjects = when (config.type) {
                ProjectListConfig.ProjectType.NEW -> {
                    // 新项目：加载所有项目
                    loadAllProjects()
                }
                ProjectListConfig.ProjectType.ALL -> {
                    // 全部项目：加载所有项目
                    loadAllProjects()
                }
                else -> {
                    // 特定分类：混合加载assets和服务器数据
                    loadHybridProjectsByCategory(config.type.id)
                }
            }

            Log.d(TAG, "加载成功: ${allProjects.size}个项目")

            // 缓存数据
            if (config.cacheStrategy != ProjectListConfig.CacheStrategy.NONE) {
                projectCache[cacheKey] = allProjects
            }

            // 过滤项目
            val filteredProjects = filterProjects(allProjects, config)
            Log.d(TAG, "过滤后: ${filteredProjects.size}个项目")

            LoadResult.Success(filteredProjects)

        } catch (e: Exception) {
            Log.e(TAG, "加载项目数据异常", e)
            LoadResult.Error("加载异常: ${e.message}", e)
        }
    }
    
    /**
     * 根据分类加载项目数据
     */
    suspend fun loadProjectsByCategory(
        config: ProjectListConfig,
        categoryId: String
    ): LoadResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "按分类加载项目: ${config.type.displayName} - $categoryId")
            
            // 先加载所有项目
            val allProjectsResult = loadProjects(config)
            
            if (allProjectsResult is LoadResult.Success) {
                // 按分类过滤
                val categoryProjects = allProjectsResult.projects.filter { project ->
                    project.category?.equals(categoryId, ignoreCase = true) == true
                }
                
                Log.d(TAG, "分类过滤后: ${categoryProjects.size}个项目")
                LoadResult.Success(categoryProjects)
            } else {
                allProjectsResult
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "按分类加载项目异常", e)
            LoadResult.Error("按分类加载异常: ${e.message}", e)
        }
    }

    /**
     * 混合加载指定分类的项目（assets + 服务器）
     * 优先加载assets数据，然后补充服务器数据
     */
    private suspend fun loadHybridProjectsByCategory(categoryId: String): List<LightweightResourceValidator.LightweightProject> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始混合加载分类项目: $categoryId")
            val allProjects = mutableListOf<LightweightResourceValidator.LightweightProject>()

            // 1. 优先加载assets数据
            if (CategoryMappingManager.hasAssetsData(categoryId)) {
                try {
                    val assetsProjects = loadProjectsByAssetCategory(categoryId)
                    allProjects.addAll(assetsProjects)
                    Log.d(TAG, "Assets数据加载成功: ${assetsProjects.size}个项目")
                } catch (e: Exception) {
                    Log.w(TAG, "Assets数据加载失败: ${e.message}")
                }
            } else {
                Log.d(TAG, "分类 $categoryId 没有对应的assets数据")
            }

            // 2. 补充服务器数据
            try {
                val serverProjects = loadServerProjectsByCategory(categoryId)
                if (serverProjects.isNotEmpty()) {
                    // 去重：避免重复添加相同ID的项目
                    val existingIds = allProjects.map { it.id }.toSet()
                    val newServerProjects = serverProjects.filter { it.id !in existingIds }
                    allProjects.addAll(newServerProjects)
                    Log.d(TAG, "服务器数据补充成功: ${newServerProjects.size}个新项目")
                } else {
                    Log.d(TAG, "服务器没有分类 $categoryId 的数据")
                }
            } catch (e: Exception) {
                Log.w(TAG, "服务器数据加载失败: ${e.message}")
            }

            Log.d(TAG, "混合加载完成: 总共${allProjects.size}个项目")
            allProjects
        } catch (e: Exception) {
            Log.e(TAG, "混合加载分类项目异常: $categoryId", e)
            // 如果混合加载失败，至少尝试返回assets数据
            try {
                if (CategoryMappingManager.hasAssetsData(categoryId)) {
                    loadProjectsByAssetCategory(categoryId)
                } else {
                    emptyList()
                }
            } catch (fallbackException: Exception) {
                Log.e(TAG, "降级加载也失败", fallbackException)
                emptyList()
            }
        }
    }



    /**
     * 加载所有本地assets项目
     */
    suspend fun loadAllLocalProjects(): List<LightweightResourceValidator.LightweightProject> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始加载所有本地项目")
            val allLocalProjects = mutableListOf<LightweightResourceValidator.LightweightProject>()

            // 获取所有assets分类
            val assetsCategories = getAssetsCategories()
            Log.d(TAG, "发现 ${assetsCategories.size} 个本地分类")

            // 加载每个分类的项目
            for (category in assetsCategories) {
                try {
                    val categoryProjects = loadProjectsByAssetCategory(category)
                    allLocalProjects.addAll(categoryProjects)
                    Log.d(TAG, "分类 $category 加载了 ${categoryProjects.size} 个本地项目")
                } catch (e: Exception) {
                    Log.w(TAG, "加载分类 $category 的本地项目失败", e)
                }
            }

            Log.d(TAG, "所有本地项目加载完成: ${allLocalProjects.size} 个项目")
            allLocalProjects
        } catch (e: Exception) {
            Log.e(TAG, "加载所有本地项目失败", e)
            emptyList()
        }
    }

    /**
     * 加载指定分类的本地项目
     */
    suspend fun loadLocalProjectsByCategory(categoryId: String): List<LightweightResourceValidator.LightweightProject> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始加载分类 $categoryId 的本地项目")

            if (CategoryMappingManager.hasAssetsData(categoryId)) {
                val localProjects = loadProjectsByAssetCategory(categoryId)
                Log.d(TAG, "分类 $categoryId 本地项目加载成功: ${localProjects.size} 个项目")
                localProjects
            } else {
                Log.d(TAG, "分类 $categoryId 没有本地数据")
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载分类 $categoryId 的本地项目失败", e)
            emptyList()
        }
    }

    /**
     * 加载所有网络项目
     */
    suspend fun loadAllNetworkProjects(): List<LightweightResourceValidator.LightweightProject> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始加载所有网络项目")
            val allNetworkProjects = mutableListOf<LightweightResourceValidator.LightweightProject>()

            // 获取服务器分类
            val serverCategories = getServerCategories()
            Log.d(TAG, "发现 ${serverCategories.size} 个服务器分类")

            // 加载每个服务器分类的项目
            for (serverCategory in serverCategories) {
                try {
                    val categoryProjects = loadServerProjectsByCategory(serverCategory)
                    allNetworkProjects.addAll(categoryProjects)
                    Log.d(TAG, "服务器分类 $serverCategory 加载了 ${categoryProjects.size} 个网络项目")
                } catch (e: Exception) {
                    Log.w(TAG, "加载服务器分类 $serverCategory 的项目失败", e)
                }
            }

            Log.d(TAG, "所有网络项目加载完成: ${allNetworkProjects.size} 个项目")
            allNetworkProjects
        } catch (e: Exception) {
            Log.e(TAG, "加载所有网络项目失败", e)
            emptyList()
        }
    }

    /**
     * 加载指定分类的网络项目
     */
    suspend fun loadNetworkProjectsByCategory(categoryId: String): List<LightweightResourceValidator.LightweightProject> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始加载分类 $categoryId 的网络项目")
            loadServerProjectsByCategory(categoryId)
        } catch (e: Exception) {
            Log.e(TAG, "加载分类 $categoryId 的网络项目失败", e)
            emptyList()
        }
    }

    /**
     * 从服务器加载指定分类的项目
     */
    private suspend fun loadServerProjectsByCategory(categoryId: String): List<LightweightResourceValidator.LightweightProject> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始从服务器加载分类项目: $categoryId")

            // 将assets分类名称映射为服务器分类名称
            val serverCategories = CategoryMappingManager.mapAssetsCategoryToServer(categoryId)
            if (serverCategories.isEmpty()) {
                Log.d(TAG, "Assets分类 $categoryId 没有对应的服务器分类")
                return@withContext emptyList()
            }

            val allServerProjects = mutableListOf<LightweightResourceValidator.LightweightProject>()

            // 遍历所有对应的服务器分类
            for (serverCategory in serverCategories) {
                Log.d(TAG, "从服务器分类 $serverCategory 加载项目")

                // 获取服务器项目列表
                val result = resourceDownloadManager.getProjectsList(category = serverCategory, pageSize = 50)

                if (result.isSuccess) {
                    val apiResponse = result.getOrNull()
                    if (apiResponse?.status == "success" && apiResponse.data != null) {
                        val serverProjects = apiResponse.data.projects
                        Log.d(TAG, "服务器分类 $serverCategory 返回 ${serverProjects.size} 个项目")

                        // 转换为LightweightProject格式
                        val lightweightProjects = serverProjects.map { remoteProject ->
                            // 构建完整的URL - 使用服务器基础URL（不包含/api/client/）
                            val serverHost = "http://**************:8083"
                            val fullJsonUrl = if (remoteProject.files.jsonUrl.startsWith("http")) {
                                remoteProject.files.jsonUrl
                            } else {
                                "$serverHost${remoteProject.files.jsonUrl}"
                            }
                            val fullOutlineUrl = if (remoteProject.files.outlineUrl.startsWith("http")) {
                                remoteProject.files.outlineUrl
                            } else {
                                "$serverHost${remoteProject.files.outlineUrl}"
                            }
                            val fullThumbnailUrl = if (remoteProject.thumbnailUrl?.startsWith("http") == true) {
                                remoteProject.thumbnailUrl
                            } else {
                                remoteProject.thumbnailUrl?.let { "$serverHost$it" }
                            }

                            // URL构建调试已完成，移除调试日志

                            LightweightResourceValidator.LightweightProject(
                                id = remoteProject.id,
                                displayName = remoteProject.displayName,
                                description = remoteProject.description,
                                category = categoryId, // 使用assets分类名称，保持一致性
                                difficulty = remoteProject.difficulty,
                                resourceSource = HybridResourceManager.Companion.ResourceSource.STREAMING,
                                jsonPath = fullJsonUrl,
                                outlinePath = fullOutlineUrl,
                                isValid = true,
                                validationErrors = emptyList(),
                                estimatedFileSize = remoteProject.files.jsonSize + remoteProject.files.outlineSize,
                                thumbnailPath = fullThumbnailUrl ?: fullOutlineUrl // 如果没有缩略图，使用轮廓图
                            )
                        }

                        allServerProjects.addAll(lightweightProjects)
                        Log.d(TAG, "成功转换 ${lightweightProjects.size} 个服务器项目")
                    } else {
                        Log.w(TAG, "服务器分类 $serverCategory API返回失败状态: ${apiResponse?.status}")
                    }
                } else {
                    Log.w(TAG, "服务器分类 $serverCategory API调用失败: ${result.exceptionOrNull()?.message}")
                }
            }

            Log.d(TAG, "总共从服务器获取 ${allServerProjects.size} 个项目")
            allServerProjects
        } catch (e: Exception) {
            Log.e(TAG, "从服务器加载分类项目异常: $categoryId", e)
            emptyList()
        }
    }

    /**
     * 过滤项目
     */
    private fun filterProjects(
        projects: List<LightweightResourceValidator.LightweightProject>,
        config: ProjectListConfig
    ): List<LightweightResourceValidator.LightweightProject> {
        return if (config.projectFilter != null) {
            projects.filter { project ->
                // 转换为HybridProject进行过滤
                val hybridProject = convertToHybridProject(project)
                config.projectFilter.invoke(hybridProject)
            }
        } else {
            projects
        }
    }
    
    /**
     * 转换为HybridProject（简化版本，用于过滤）
     */
    private fun convertToHybridProject(
        lightweightProject: LightweightResourceValidator.LightweightProject
    ): HybridResourceManager.HybridProject {
        return HybridResourceManager.HybridProject(
            id = lightweightProject.id,
            name = lightweightProject.id,
            displayName = lightweightProject.displayName,
            description = lightweightProject.description ?: "",
            category = lightweightProject.category ?: "other",
            difficulty = lightweightProject.difficulty,
            totalRegions = 0,
            totalColors = 0,
            estimatedTime = 0,
            thumbnailUrl = "",
            previewUrl = "",
            resourceType = HybridResourceManager.Companion.ResourceType.LOCAL_ASSET,
            resourceSource = lightweightProject.resourceSource,
            version = "1.0",
            fileSize = lightweightProject.estimatedFileSize,
            isDownloaded = false,
            isBuiltIn = lightweightProject.resourceSource == HybridResourceManager.Companion.ResourceSource.BUILT_IN,
            downloadProgress = 0f,
            tags = emptyList(),
            releaseDate = null,
            popularity = 0,
            rating = 0.0f
        )
    }
    
    /**
     * 清理缓存
     */
    fun clearCache(type: ProjectListConfig.ProjectType? = null) {
        if (type != null) {
            projectCache.remove(type.id)
            categoryCache.remove(type.id)
            Log.d(TAG, "清理缓存: ${type.displayName}")
        } else {
            projectCache.clear()
            categoryCache.clear()
            Log.d(TAG, "清理所有缓存")
        }
    }
    
    /**
     * 加载所有项目（从原有的验证器）
     */
    private suspend fun loadAllProjects(): List<LightweightResourceValidator.LightweightProject> {
        val result = lightweightValidator.getLightweightProjects()
        return if (result.isSuccess) {
            result.getOrNull() ?: emptyList()
        } else {
            throw result.exceptionOrNull() ?: Exception("加载项目失败")
        }
    }

    /**
     * 按assets分类文件夹加载项目
     */
    private suspend fun loadProjectsByAssetCategory(categoryName: String): List<LightweightResourceValidator.LightweightProject> {
        try {
            Log.d(TAG, "从assets分类加载项目: $categoryName")

            val assetManager = context.assets
            val categoryFiles = assetManager.list(categoryName) ?: emptyArray()
            val projects = mutableListOf<LightweightResourceValidator.LightweightProject>()

            // 找到所有JSON文件
            val jsonFiles = categoryFiles.filter { it.endsWith(".json") }
            Log.d(TAG, "分类 $categoryName 中发现 ${jsonFiles.size} 个JSON文件")

            for (jsonFile in jsonFiles) {
                try {
                    val projectId = jsonFile.removeSuffix(".json")
                    val jsonPath = "$categoryName/$jsonFile"
                    val pngFile = "$projectId.png"
                    val pngPath = "$categoryName/$pngFile"

                    // 检查对应的PNG文件是否存在
                    val hasPngFile = try {
                        assetManager.open(pngPath).use { true }
                    } catch (e: Exception) {
                        false
                    }

                    if (hasPngFile) {
                        // 创建轻量级项目对象
                        val project = LightweightResourceValidator.LightweightProject(
                            id = projectId,
                            displayName = formatDisplayName(projectId),
                            description = "来自分类: $categoryName",
                            category = categoryName,
                            difficulty = extractDifficultyFromId(projectId),
                            resourceSource = HybridResourceManager.Companion.ResourceSource.BUILT_IN,
                            jsonPath = jsonPath,
                            outlinePath = pngPath,
                            isValid = true,
                            validationErrors = emptyList(),
                            estimatedFileSize = 0L // 可以后续计算
                        )

                        projects.add(project)
                        Log.d(TAG, "添加项目: $projectId (分类: $categoryName)")
                    } else {
                        Log.w(TAG, "项目 $projectId 缺少PNG文件: $pngPath")
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "处理项目文件失败: $jsonFile", e)
                }
            }

            Log.d(TAG, "分类 $categoryName 加载完成: ${projects.size} 个项目")
            return projects

        } catch (e: Exception) {
            Log.e(TAG, "加载分类项目失败: $categoryName", e)
            throw e
        }
    }

    /**
     * 格式化显示名称
     */
    private fun formatDisplayName(projectId: String): String {
        return projectId.split("-", "_")
            .joinToString(" ") { word ->
                word.replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }
            }
    }

    /**
     * 从项目ID提取难度
     */
    private fun extractDifficultyFromId(projectId: String): String {
        return when {
            projectId.contains("easy") || projectId.endsWith("1") -> "easy"
            projectId.contains("hard") || projectId.endsWith("3") -> "hard"
            else -> "medium"
        }
    }

    /**
     * 获取分类配置
     */
    fun getConfigForCategory(categoryName: String): ProjectListConfig {
        return ProjectListConfig.createDynamicCategoryConfig(categoryName)
    }

    /**
     * 获取缓存信息
     */
    fun getCacheInfo(): String {
        return "项目缓存: ${projectCache.size}个类型, 分类缓存: ${categoryCache.size}个类型"
    }
}
