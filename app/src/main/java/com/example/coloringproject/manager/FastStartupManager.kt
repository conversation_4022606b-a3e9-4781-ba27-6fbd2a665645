package com.example.coloringproject.manager

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 快速启动管理器
 * 专门优化应用启动时的加载性能
 */
class FastStartupManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "FastStartupManager"
        
        @Volatile
        private var INSTANCE: FastStartupManager? = null
        
        fun getInstance(context: Context): FastStartupManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FastStartupManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    /**
     * 获取默认分类列表（用于快速启动）
     * 不依赖网络请求，立即返回
     */
    fun getDefaultCategories(): List<String> {
        return listOf("animal", "building", "castle", "gardens", "treehouse")
    }
    
    /**
     * 检查是否有本地assets分类
     * 快速检查，不进行详细扫描
     */
    suspend fun hasLocalCategories(): Boolean = withContext(Dispatchers.IO) {
        try {
            val assetManager = context.assets
            val assetFiles = assetManager.list("") ?: emptyArray()
            
            // 快速检查是否有常见的分类文件夹
            val commonCategories = listOf("animal", "building", "castle", "gardens", "treehouse")
            return@withContext commonCategories.any { category ->
                try {
                    val subFiles = assetManager.list(category)
                    subFiles != null && subFiles.any { it.endsWith(".json") }
                } catch (e: Exception) {
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查本地分类失败", e)
            false
        }
    }
    
    /**
     * 获取快速启动分类列表
     * 优先使用本地分类，如果没有则使用默认分类
     */
    suspend fun getFastStartupCategories(): List<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "获取快速启动分类")
            
            // 快速检查本地分类
            if (hasLocalCategories()) {
                Log.d(TAG, "发现本地分类，使用ProjectDataManager获取详细列表")
                try {
                    val projectDataManager = ProjectDataManager.getInstance(context)
                    // 使用反射或其他方式获取assets分类，这里先使用默认分类
                    val defaultCategories = getDefaultCategories()
                    Log.d(TAG, "使用默认分类: ${defaultCategories.joinToString(", ")}")
                    return@withContext defaultCategories
                } catch (e: Exception) {
                    Log.w(TAG, "获取本地分类失败，使用默认分类", e)
                }
            }
            
            // 如果没有本地分类，使用默认分类
            val defaultCategories = getDefaultCategories()
            Log.d(TAG, "使用默认分类: ${defaultCategories.joinToString(", ")}")
            return@withContext defaultCategories
            
        } catch (e: Exception) {
            Log.e(TAG, "获取快速启动分类失败", e)
            return@withContext getDefaultCategories()
        }
    }
    
    /**
     * 预热关键组件
     * 在后台预先初始化一些重要的单例对象
     */
    suspend fun preWarmComponents() = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始预热关键组件")
            
            // 预热ProjectDataManager
            ProjectDataManager.getInstance(context)
            
            // 预热LibraryLoadStrategy
            LibraryLoadStrategy.getInstance(context)
            
            Log.d(TAG, "关键组件预热完成")
        } catch (e: Exception) {
            Log.e(TAG, "预热组件失败", e)
        }
    }
    
    /**
     * 获取启动性能统计
     */
    fun getStartupStats(): String {
        return "FastStartupManager统计信息"
    }
}

