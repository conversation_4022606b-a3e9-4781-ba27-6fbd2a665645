package com.example.coloringproject.manager

import android.util.Log

/**
 * 分类映射管理器
 * 负责处理服务器分类和assets分类之间的映射关系
 */
object CategoryMappingManager {
    
    private const val TAG = "CategoryMappingManager"
    
    /**
     * 服务器分类名称到assets分类名称的映射
     */
    private val serverToAssetsMapping = mapOf(
        // 服务器分类名 -> assets分类名
        "Animals" to "animal",
        "Houses" to "building", 
        "Castles" to "castle",
        "Plants" to "gardens",
        "Treehouses" to "treehouse",
        "Farmhouses" to "building", // 农场房屋归类到建筑
        "Cartoon" to "animal", // 卡通角色暂时归类到动物
        "Vehicles" to "building", // 交通工具暂时归类到建筑
        "Food" to "gardens", // 食物暂时归类到花园
        "Holidays" to "gardens" // 节日主题暂时归类到花园
    )
    
    /**
     * assets分类名称到中文显示名称的映射
     */
    private val assetsToDisplayMapping = mapOf(
        "animal" to "动物",
        "building" to "建筑",
        "castle" to "城堡", 
        "gardens" to "花园",
        "treehouse" to "树屋"
    )
    
    /**
     * 服务器分类名称到中文显示名称的映射
     */
    private val serverToDisplayMapping = mapOf(
        "Animals" to "动物",
        "Houses" to "建筑",
        "Castles" to "城堡",
        "Plants" to "花园",
        "Treehouses" to "树屋",
        "Farmhouses" to "农场",
        "Cartoon" to "卡通",
        "Vehicles" to "交通",
        "Food" to "美食",
        "Holidays" to "节日"
    )
    
    /**
     * 将服务器分类名称映射到assets分类名称
     */
    fun mapServerCategoryToAssets(serverCategory: String): String? {
        val mapped = serverToAssetsMapping[serverCategory]
        Log.d(TAG, "映射服务器分类: $serverCategory -> $mapped")
        return mapped
    }
    
    /**
     * 获取分类的中文显示名称
     * 优先使用assets分类映射，如果没有则使用服务器分类映射
     */
    fun getCategoryDisplayName(categoryName: String): String {
        // 先尝试assets分类映射
        assetsToDisplayMapping[categoryName]?.let { displayName ->
            Log.d(TAG, "使用assets映射: $categoryName -> $displayName")
            return displayName
        }
        
        // 再尝试服务器分类映射
        serverToDisplayMapping[categoryName]?.let { displayName ->
            Log.d(TAG, "使用服务器映射: $categoryName -> $displayName")
            return displayName
        }
        
        // 如果都没有，返回首字母大写的原名称
        val fallback = categoryName.replaceFirstChar { it.uppercase() }
        Log.d(TAG, "使用默认映射: $categoryName -> $fallback")
        return fallback
    }
    
    /**
     * 检查分类是否有对应的assets数据
     */
    fun hasAssetsData(categoryName: String): Boolean {
        val hasAssets = assetsToDisplayMapping.containsKey(categoryName)
        Log.d(TAG, "检查assets数据: $categoryName -> $hasAssets")
        return hasAssets
    }
    
    /**
     * 获取所有支持的assets分类
     */
    fun getSupportedAssetsCategories(): List<String> {
        return assetsToDisplayMapping.keys.toList()
    }
    
    /**
     * 获取服务器分类对应的assets分类
     * 用于将服务器项目数据补充到对应的assets分类中
     */
    fun getAssetsCategoryForServerCategory(serverCategory: String): String? {
        return serverToAssetsMapping[serverCategory]
    }
    
    /**
     * 合并分类列表，去重并保持assets分类优先
     */
    fun mergeCategories(assetsCategories: List<String>, serverCategories: List<String>): List<String> {
        Log.d(TAG, "合并分类列表")
        Log.d(TAG, "Assets分类: ${assetsCategories.joinToString(", ")}")
        Log.d(TAG, "服务器分类: ${serverCategories.joinToString(", ")}")
        
        val mergedCategories = mutableSetOf<String>()
        
        // 1. 首先添加所有assets分类（保持优先级）
        mergedCategories.addAll(assetsCategories)
        
        // 2. 添加服务器分类中有对应assets映射的分类
        for (serverCategory in serverCategories) {
            val assetsCategory = mapServerCategoryToAssets(serverCategory)
            if (assetsCategory != null) {
                mergedCategories.add(assetsCategory)
            }
        }
        
        // 3. 按预定义顺序排序
        val preferredOrder = listOf("animal", "building", "castle", "gardens", "treehouse")
        val result = preferredOrder.filter { it in mergedCategories } + 
                    (mergedCategories - preferredOrder.toSet()).sorted()
        
        Log.d(TAG, "合并后分类: ${result.joinToString(", ")}")
        return result
    }
    
    /**
     * 检查服务器分类是否应该补充到指定的assets分类中
     */
    fun shouldSupplementCategory(assetsCategory: String, serverCategory: String): Boolean {
        val mappedCategory = mapServerCategoryToAssets(serverCategory)
        val shouldSupplement = mappedCategory == assetsCategory
        Log.d(TAG, "检查补充: assets=$assetsCategory, server=$serverCategory -> $shouldSupplement")
        return shouldSupplement
    }

    /**
     * 将assets分类名称映射回对应的服务器分类名称列表
     * 一个assets分类可能对应多个服务器分类
     */
    fun mapAssetsCategoryToServer(assetsCategory: String): List<String> {
        val serverCategories = mutableListOf<String>()

        // 遍历所有服务器分类，找到映射到指定assets分类的服务器分类
        for ((serverCategory, mappedAssetsCategory) in serverToAssetsMapping) {
            if (mappedAssetsCategory == assetsCategory) {
                serverCategories.add(serverCategory)
            }
        }

        Log.d(TAG, "Assets分类 $assetsCategory 对应的服务器分类: ${serverCategories.joinToString(", ")}")
        return serverCategories
    }
}
